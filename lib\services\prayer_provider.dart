import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';
import '../utils/platform_helper.dart';

/// نموذج أوقات الصلاة
class PrayerTimes {
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;
  final DateTime date;

  PrayerTimes({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
    required this.date,
  });

  Map<String, dynamic> toJson() {
    return {
      'fajr': fajr.toIso8601String(),
      'sunrise': sunrise.toIso8601String(),
      'dhuhr': dhuhr.toIso8601String(),
      'asr': asr.toIso8601String(),
      'maghrib': maghrib.toIso8601String(),
      'isha': isha.toIso8601String(),
      'date': date.toIso8601String(),
    };
  }

  factory PrayerTimes.fromJson(Map<String, dynamic> json) {
    return PrayerTimes(
      fajr: DateTime.parse(json['fajr']),
      sunrise: DateTime.parse(json['sunrise']),
      dhuhr: DateTime.parse(json['dhuhr']),
      asr: DateTime.parse(json['asr']),
      maghrib: DateTime.parse(json['maghrib']),
      isha: DateTime.parse(json['isha']),
      date: DateTime.parse(json['date']),
    );
  }
}

/// مزود أوقات الصلاة
class PrayerProvider extends ChangeNotifier {
  PrayerTimes? _currentPrayerTimes;
  Position? _currentPosition;
  String _currentCity = '';
  bool _isLoading = false;
  bool _locationPermissionGranted = false;

  /// الحصول على أوقات الصلاة الحالية
  PrayerTimes? get currentPrayerTimes => _currentPrayerTimes;

  /// الحصول على الموقع الحالي
  Position? get currentPosition => _currentPosition;

  /// الحصول على اسم المدينة الحالية
  String get currentCity => _currentCity;

  /// الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على حالة إذن الموقع
  bool get locationPermissionGranted => _locationPermissionGranted;

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _loadSavedData();
      await _requestLocationPermission();
      if (_locationPermissionGranted) {
        await _getCurrentLocation();
        await _calculatePrayerTimes();
      }
      AppLogger.info('تم تهيئة مزود أوقات الصلاة بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود أوقات الصلاة: $e');
    }
  }

  /// طلب إذن الموقع
  Future<void> _requestLocationPermission() async {
    try {
      if (!PlatformHelper.supportsLocation) {
        AppLogger.info('المنصة الحالية لا تدعم تحديد الموقع');
        return;
      }

      final permission = await Permission.location.request();
      _locationPermissionGranted = permission.isGranted;
      
      if (!_locationPermissionGranted) {
        AppLogger.warning('لم يتم منح إذن الموقع');
      }
    } catch (e) {
      AppLogger.error('خطأ في طلب إذن الموقع: $e');
    }
  }

  /// الحصول على الموقع الحالي
  Future<void> _getCurrentLocation() async {
    try {
      if (!_locationPermissionGranted) return;

      _isLoading = true;
      notifyListeners();

      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // يمكن إضافة خدمة للحصول على اسم المدينة من الإحداثيات
      _currentCity = 'الموقع الحالي';

      await _saveLocationData();
      
      _isLoading = false;
      notifyListeners();

      AppLogger.info('تم الحصول على الموقع الحالي: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}');
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      AppLogger.error('خطأ في الحصول على الموقع: $e');
    }
  }

  /// حساب أوقات الصلاة
  Future<void> _calculatePrayerTimes() async {
    try {
      if (_currentPosition == null) return;

      // هنا يمكن استخدام مكتبة حساب أوقات الصلاة
      // مثل مكتبة adhan أو حساب مخصص
      
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      
      // أوقات افتراضية للتجربة - يجب استبدالها بحساب حقيقي
      _currentPrayerTimes = PrayerTimes(
        fajr: today.add(const Duration(hours: 5, minutes: 30)),
        sunrise: today.add(const Duration(hours: 6, minutes: 45)),
        dhuhr: today.add(const Duration(hours: 12, minutes: 15)),
        asr: today.add(const Duration(hours: 15, minutes: 30)),
        maghrib: today.add(const Duration(hours: 18, minutes: 0)),
        isha: today.add(const Duration(hours: 19, minutes: 30)),
        date: today,
      );

      await _savePrayerTimes();
      notifyListeners();

      AppLogger.info('تم حساب أوقات الصلاة بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في حساب أوقات الصلاة: $e');
    }
  }

  /// تحميل البيانات المحفوظة
  Future<void> _loadSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل الموقع المحفوظ
      final latitude = prefs.getDouble('saved_latitude');
      final longitude = prefs.getDouble('saved_longitude');
      
      if (latitude != null && longitude != null) {
        _currentPosition = Position(
          latitude: latitude,
          longitude: longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
      }
      
      // تحميل اسم المدينة
      _currentCity = prefs.getString('saved_city') ?? '';
      
      // تحميل أوقات الصلاة المحفوظة
      final prayerTimesJson = prefs.getString('saved_prayer_times');
      if (prayerTimesJson != null) {
        final Map<String, dynamic> data = Map<String, dynamic>.from(
          Uri.splitQueryString(prayerTimesJson),
        );
        _currentPrayerTimes = PrayerTimes.fromJson(data);
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل البيانات المحفوظة: $e');
    }
  }

  /// حفظ بيانات الموقع
  Future<void> _saveLocationData() async {
    try {
      if (_currentPosition == null) return;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('saved_latitude', _currentPosition!.latitude);
      await prefs.setDouble('saved_longitude', _currentPosition!.longitude);
      await prefs.setString('saved_city', _currentCity);
    } catch (e) {
      AppLogger.error('خطأ في حفظ بيانات الموقع: $e');
    }
  }

  /// حفظ أوقات الصلاة
  Future<void> _savePrayerTimes() async {
    try {
      if (_currentPrayerTimes == null) return;
      
      final prefs = await SharedPreferences.getInstance();
      final prayerTimesJson = Uri(queryParameters: _currentPrayerTimes!.toJson().map(
        (key, value) => MapEntry(key, value.toString()),
      )).query;
      
      await prefs.setString('saved_prayer_times', prayerTimesJson);
    } catch (e) {
      AppLogger.error('خطأ في حفظ أوقات الصلاة: $e');
    }
  }

  /// تحديث الموقع وأوقات الصلاة
  Future<void> refreshPrayerTimes() async {
    try {
      await _getCurrentLocation();
      await _calculatePrayerTimes();
      AppLogger.info('تم تحديث أوقات الصلاة');
    } catch (e) {
      AppLogger.error('خطأ في تحديث أوقات الصلاة: $e');
    }
  }

  /// الحصول على الصلاة التالية
  String getNextPrayer() {
    if (_currentPrayerTimes == null) return '';
    
    final now = DateTime.now();
    final prayers = [
      {'name': 'الفجر', 'time': _currentPrayerTimes!.fajr},
      {'name': 'الشروق', 'time': _currentPrayerTimes!.sunrise},
      {'name': 'الظهر', 'time': _currentPrayerTimes!.dhuhr},
      {'name': 'العصر', 'time': _currentPrayerTimes!.asr},
      {'name': 'المغرب', 'time': _currentPrayerTimes!.maghrib},
      {'name': 'العشاء', 'time': _currentPrayerTimes!.isha},
    ];
    
    for (final prayer in prayers) {
      if ((prayer['time'] as DateTime).isAfter(now)) {
        return prayer['name'] as String;
      }
    }
    
    return 'الفجر'; // الصلاة التالية هي فجر اليوم التالي
  }

  /// الحصول على الوقت المتبقي للصلاة التالية
  Duration getTimeToNextPrayer() {
    if (_currentPrayerTimes == null) return Duration.zero;
    
    final now = DateTime.now();
    final prayers = [
      _currentPrayerTimes!.fajr,
      _currentPrayerTimes!.sunrise,
      _currentPrayerTimes!.dhuhr,
      _currentPrayerTimes!.asr,
      _currentPrayerTimes!.maghrib,
      _currentPrayerTimes!.isha,
    ];
    
    for (final prayerTime in prayers) {
      if (prayerTime.isAfter(now)) {
        return prayerTime.difference(now);
      }
    }
    
    // إذا انتهت جميع صلوات اليوم، احسب الوقت لفجر اليوم التالي
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final fajrTomorrow = tomorrow.add(const Duration(hours: 5, minutes: 30));
    return fajrTomorrow.difference(now);
  }
}
