import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/statistics_model.dart';
import 'statistics_provider.dart';
import '../utils/logger.dart';

/// مزود الأذكار - يدير حالة الأذكار والتفاعل معها
class AzkarProvider extends ChangeNotifier {
  StatisticsProvider? _statisticsProvider;
  
  // قائمة الأذكار المفضلة
  List<String> _favoriteAzkars = [];
  
  // عداد التسبيح الحالي
  int _currentTasbihCount = 0;
  
  // الذكر الحالي المحدد
  String? _currentZikr;
  
  // حالة تشغيل الصوت
  bool _isSoundEnabled = true;
  
  // حالة الاهتزاز
  bool _isVibrationEnabled = true;

  /// الحصول على قائمة الأذكار المفضلة
  List<String> get favoriteAzkars => _favoriteAzkars;

  /// الحصول على عداد التسبيح الحالي
  int get currentTasbihCount => _currentTasbihCount;

  /// الحصول على الذكر الحالي
  String? get currentZikr => _currentZikr;

  /// الحصول على حالة تشغيل الصوت
  bool get isSoundEnabled => _isSoundEnabled;

  /// الحصول على حالة الاهتزاز
  bool get isVibrationEnabled => _isVibrationEnabled;

  /// تعيين مزود الإحصائيات
  void setStatisticsProvider(StatisticsProvider statisticsProvider) {
    _statisticsProvider = statisticsProvider;
  }

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _loadSettings();
      AppLogger.info('تم تهيئة مزود الأذكار بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود الأذكار: $e');
    }
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل الأذكار المفضلة
      _favoriteAzkars = prefs.getStringList('favorite_azkars') ?? [];
      
      // تحميل إعدادات الصوت والاهتزاز
      _isSoundEnabled = prefs.getBool('sound_enabled') ?? true;
      _isVibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
      
      // تحميل عداد التسبيح
      _currentTasbihCount = prefs.getInt('current_tasbih_count') ?? 0;
      
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل إعدادات الأذكار: $e');
    }
  }

  /// حفظ الإعدادات في التخزين المحلي
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setStringList('favorite_azkars', _favoriteAzkars);
      await prefs.setBool('sound_enabled', _isSoundEnabled);
      await prefs.setBool('vibration_enabled', _isVibrationEnabled);
      await prefs.setInt('current_tasbih_count', _currentTasbihCount);
    } catch (e) {
      AppLogger.error('خطأ في حفظ إعدادات الأذكار: $e');
    }
  }

  /// إضافة ذكر إلى المفضلة
  Future<void> addToFavorites(String zikr) async {
    if (!_favoriteAzkars.contains(zikr)) {
      _favoriteAzkars.add(zikr);
      await _saveSettings();
      notifyListeners();
      AppLogger.info('تم إضافة الذكر إلى المفضلة: $zikr');
    }
  }

  /// إزالة ذكر من المفضلة
  Future<void> removeFromFavorites(String zikr) async {
    if (_favoriteAzkars.contains(zikr)) {
      _favoriteAzkars.remove(zikr);
      await _saveSettings();
      notifyListeners();
      AppLogger.info('تم إزالة الذكر من المفضلة: $zikr');
    }
  }

  /// التحقق من وجود ذكر في المفضلة
  bool isFavorite(String zikr) {
    return _favoriteAzkars.contains(zikr);
  }

  /// تعيين الذكر الحالي
  void setCurrentZikr(String zikr) {
    _currentZikr = zikr;
    _currentTasbihCount = 0;
    notifyListeners();
    AppLogger.info('تم تعيين الذكر الحالي: $zikr');
  }

  /// زيادة عداد التسبيح
  Future<void> incrementTasbihCount() async {
    _currentTasbihCount++;
    await _saveSettings();
    
    // تسجيل الإحصائية
    if (_statisticsProvider != null && _currentZikr != null) {
      await _statisticsProvider!.recordZikrCount(_currentZikr!, 1);
    }
    
    notifyListeners();
  }

  /// إعادة تعيين عداد التسبيح
  Future<void> resetTasbihCount() async {
    _currentTasbihCount = 0;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم إعادة تعيين عداد التسبيح');
  }

  /// تبديل حالة الصوت
  Future<void> toggleSound() async {
    _isSoundEnabled = !_isSoundEnabled;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم تبديل حالة الصوت: $_isSoundEnabled');
  }

  /// تبديل حالة الاهتزاز
  Future<void> toggleVibration() async {
    _isVibrationEnabled = !_isVibrationEnabled;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم تبديل حالة الاهتزاز: $_isVibrationEnabled');
  }

  /// تسجيل قراءة ذكر
  Future<void> recordZikrRead(String zikr) async {
    if (_statisticsProvider != null) {
      await _statisticsProvider!.recordZikrCount(zikr, 1);
      AppLogger.info('تم تسجيل قراءة الذكر: $zikr');
    }
  }

  /// الحصول على إحصائيات الأذكار
  Map<String, int> getZikrStatistics() {
    if (_statisticsProvider != null) {
      return _statisticsProvider!.getZikrStatistics();
    }
    return {};
  }
}
