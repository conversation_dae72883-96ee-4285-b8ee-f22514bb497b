import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'statistics_provider.dart';
import '../utils/logger.dart';
import '../models/azkar_model.dart';

/// مزود الأذكار - يدير حالة الأذكار والتفاعل معها
class AzkarProvider extends ChangeNotifier {
  StatisticsProvider? _statisticsProvider;

  // قائمة الأذكار المفضلة
  List<String> _favoriteAzkars = [];

  // عداد التسبيح الحالي
  int _currentTasbihCount = 0;

  // الذكر الحالي المحدد
  String? _currentZikr;

  // حالة تشغيل الصوت
  bool _isSoundEnabled = true;

  // حالة الاهتزاز
  bool _isVibrationEnabled = true;

  // حالة التحميل
  bool _isLoading = false;

  // قائمة التصنيفات
  List<Category> _categories = [];

  // الذكر اليومي
  Zikr? _dailyZikr;

  // الأذكار المخصصة
  List<Zikr> _customAzkar = [];

  // الأذكار الحالية
  List<Zikr> _currentAzkar = [];

  /// الحصول على قائمة الأذكار المفضلة
  List<String> get favoriteAzkars => _favoriteAzkars;

  /// الحصول على عداد التسبيح الحالي
  int get currentTasbihCount => _currentTasbihCount;

  /// الحصول على الذكر الحالي
  String? get currentZikr => _currentZikr;

  /// الحصول على حالة تشغيل الصوت
  bool get isSoundEnabled => _isSoundEnabled;

  /// الحصول على حالة الاهتزاز
  bool get isVibrationEnabled => _isVibrationEnabled;

  /// الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على قائمة التصنيفات
  List<Category> get categories => _categories;

  /// الحصول على الذكر اليومي
  Zikr? get dailyZikr => _dailyZikr;

  /// الحصول على الأذكار المخصصة
  List<Zikr> get customAzkar => _customAzkar;

  /// الحصول على الأذكار الحالية
  List<Zikr> get currentAzkar => _currentAzkar;

  /// تعيين مزود الإحصائيات
  void setStatisticsProvider(StatisticsProvider statisticsProvider) {
    _statisticsProvider = statisticsProvider;
  }

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _loadSettings();
      AppLogger.info('تم تهيئة مزود الأذكار بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود الأذكار: $e');
    }
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل الأذكار المفضلة
      _favoriteAzkars = prefs.getStringList('favorite_azkars') ?? [];

      // تحميل إعدادات الصوت والاهتزاز
      _isSoundEnabled = prefs.getBool('sound_enabled') ?? true;
      _isVibrationEnabled = prefs.getBool('vibration_enabled') ?? true;

      // تحميل عداد التسبيح
      _currentTasbihCount = prefs.getInt('current_tasbih_count') ?? 0;

      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل إعدادات الأذكار: $e');
    }
  }

  /// حفظ الإعدادات في التخزين المحلي
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setStringList('favorite_azkars', _favoriteAzkars);
      await prefs.setBool('sound_enabled', _isSoundEnabled);
      await prefs.setBool('vibration_enabled', _isVibrationEnabled);
      await prefs.setInt('current_tasbih_count', _currentTasbihCount);
    } catch (e) {
      AppLogger.error('خطأ في حفظ إعدادات الأذكار: $e');
    }
  }

  /// إضافة ذكر إلى المفضلة
  Future<void> addToFavorites(String zikr) async {
    if (!_favoriteAzkars.contains(zikr)) {
      _favoriteAzkars.add(zikr);
      await _saveSettings();
      notifyListeners();
      AppLogger.info('تم إضافة الذكر إلى المفضلة: $zikr');
    }
  }

  /// إزالة ذكر من المفضلة
  Future<void> removeFromFavorites(String zikr) async {
    if (_favoriteAzkars.contains(zikr)) {
      _favoriteAzkars.remove(zikr);
      await _saveSettings();
      notifyListeners();
      AppLogger.info('تم إزالة الذكر من المفضلة: $zikr');
    }
  }

  /// التحقق من وجود ذكر في المفضلة
  bool isFavorite(String zikr) {
    return _favoriteAzkars.contains(zikr);
  }

  /// تعيين الذكر الحالي
  void setCurrentZikr(String zikr) {
    _currentZikr = zikr;
    _currentTasbihCount = 0;
    notifyListeners();
    AppLogger.info('تم تعيين الذكر الحالي: $zikr');
  }

  /// زيادة عداد التسبيح
  Future<void> incrementTasbihCount() async {
    _currentTasbihCount++;
    await _saveSettings();

    // تسجيل الإحصائية
    if (_statisticsProvider != null && _currentZikr != null) {
      await _statisticsProvider!.recordZikrCount(_currentZikr!, 1);
    }

    notifyListeners();
  }

  /// إعادة تعيين عداد التسبيح
  Future<void> resetTasbihCount() async {
    _currentTasbihCount = 0;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم إعادة تعيين عداد التسبيح');
  }

  /// تبديل حالة الصوت
  Future<void> toggleSound() async {
    _isSoundEnabled = !_isSoundEnabled;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم تبديل حالة الصوت: $_isSoundEnabled');
  }

  /// تبديل حالة الاهتزاز
  Future<void> toggleVibration() async {
    _isVibrationEnabled = !_isVibrationEnabled;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم تبديل حالة الاهتزاز: $_isVibrationEnabled');
  }

  /// تسجيل قراءة ذكر
  Future<void> recordZikrRead(String zikr) async {
    if (_statisticsProvider != null) {
      await _statisticsProvider!.recordZikrCount(zikr, 1);
      AppLogger.info('تم تسجيل قراءة الذكر: $zikr');
    }
  }

  /// الحصول على إحصائيات الأذكار
  Map<String, int> getZikrStatistics() {
    if (_statisticsProvider != null) {
      return _statisticsProvider!.getZikrStatistics();
    }
    return {};
  }

  /// إعادة تحميل البيانات
  Future<void> reloadData() async {
    try {
      _isLoading = true;
      notifyListeners();

      // تحميل التصنيفات الافتراضية
      _categories = _getDefaultCategories();

      // تحميل الذكر اليومي
      _dailyZikr = _getDefaultDailyZikr();

      // تحميل الأذكار المخصصة
      await _loadCustomAzkar();

      _isLoading = false;
      notifyListeners();

      AppLogger.info('تم إعادة تحميل بيانات الأذكار بنجاح');
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      AppLogger.error('خطأ في إعادة تحميل بيانات الأذكار: $e');
    }
  }

  /// الحصول على التصنيفات الافتراضية
  List<Category> _getDefaultCategories() {
    return [
      Category(
        id: 1,
        name: 'أذكار الصباح',
        description: 'أذكار تقال في الصباح',
        iconName: 'morning',
        azkarCount: 10,
      ),
      Category(
        id: 2,
        name: 'أذكار المساء',
        description: 'أذكار تقال في المساء',
        iconName: 'evening',
        azkarCount: 10,
      ),
      Category(
        id: 3,
        name: 'أذكار النوم',
        description: 'أذكار تقال قبل النوم',
        iconName: 'sleep',
        azkarCount: 8,
      ),
      Category(
        id: 4,
        name: 'أذكار الطعام',
        description: 'أذكار تقال قبل وبعد الطعام',
        iconName: 'food',
        azkarCount: 5,
      ),
    ];
  }

  /// الحصول على الذكر اليومي الافتراضي
  Zikr _getDefaultDailyZikr() {
    return Zikr(
      id: 1,
      text: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
      source: 'صحيح البخاري',
      count: 100,
      categoryId: 1,
      translation: 'تنزيه الله عن كل نقص مع حمده',
      benefits: 'من قالها في يوم مائة مرة حطت خطاياه وإن كانت مثل زبد البحر',
    );
  }

  /// تحميل الأذكار المخصصة
  Future<void> _loadCustomAzkar() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customAzkarJson = prefs.getStringList('custom_azkar') ?? [];

      _customAzkar = customAzkarJson.map((json) {
        final Map<String, dynamic> data = Map<String, dynamic>.from(
          Uri.splitQueryString(json),
        );
        return Zikr.fromJson(data);
      }).toList();
    } catch (e) {
      AppLogger.error('خطأ في تحميل الأذكار المخصصة: $e');
    }
  }

  /// حفظ الأذكار المخصصة
  Future<void> _saveCustomAzkar() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customAzkarJson = _customAzkar.map((zikr) {
        return Uri(
            queryParameters: zikr.toJson().map(
                  (key, value) => MapEntry(key, value.toString()),
                )).query;
      }).toList();

      await prefs.setStringList('custom_azkar', customAzkarJson);
    } catch (e) {
      AppLogger.error('خطأ في حفظ الأذكار المخصصة: $e');
    }
  }

  /// تبديل حالة المفضلة للذكر المخصص
  Future<void> toggleCustomZikrFavorite(int zikrId) async {
    try {
      final index = _customAzkar.indexWhere((zikr) => zikr.id == zikrId);
      if (index != -1) {
        _customAzkar[index] = _customAzkar[index].copyWith(
          isFavorite: !_customAzkar[index].isFavorite,
        );
        await _saveCustomAzkar();
        notifyListeners();
      }
    } catch (e) {
      AppLogger.error('خطأ في تبديل حالة المفضلة: $e');
    }
  }

  /// تحديث عداد الذكر المخصص
  Future<void> updateCustomZikrCount(int zikrId, int newCount) async {
    try {
      final index = _customAzkar.indexWhere((zikr) => zikr.id == zikrId);
      if (index != -1) {
        _customAzkar[index] = _customAzkar[index].copyWith(
          currentCount: newCount,
        );
        await _saveCustomAzkar();
        notifyListeners();
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث عداد الذكر: $e');
    }
  }

  /// حذف الذكر المخصص
  Future<void> deleteCustomZikr(int zikrId) async {
    try {
      _customAzkar.removeWhere((zikr) => zikr.id == zikrId);
      await _saveCustomAzkar();
      notifyListeners();
      AppLogger.info('تم حذف الذكر المخصص');
    } catch (e) {
      AppLogger.error('خطأ في حذف الذكر المخصص: $e');
    }
  }
}
