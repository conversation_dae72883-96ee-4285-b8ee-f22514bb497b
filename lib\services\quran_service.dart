import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_model.dart';
import '../utils/logger.dart';

/// خدمة للتعامل مع بيانات القرآن الكريم
class QuranService {
  static const String _apiUrl = 'https://api.alquran.cloud/v1/quran/ar.alafasy';
  static const String _localDataKey = 'quran_data';
  static const String _localDataTimestampKey = 'quran_data_timestamp';
  static const int _cacheExpiryDays =
      30; // مدة صلاحية البيانات المخزنة (30 يوم)

  /// الحصول على قائمة السور
  Future<List<Surah>> getSurahs() async {
    try {
      final quranData = await _getQuranData();
      return quranData.surahs;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على قائمة السور: $e');
      // محاولة تحميل البيانات من ملف الأصول كخطة بديلة
      try {
        final jsonString =
            await rootBundle.loadString('assets/data/quran.json');
        final jsonData = json.decode(jsonString);
        final List<dynamic> surahsJson = jsonData['data']['surahs'];
        return surahsJson
            .map((surahJson) => Surah.fromJson(surahJson))
            .toList();
      } catch (assetError) {
        AppLogger.error('خطأ في تحميل بيانات السور من ملف الأصول: $assetError');
        return [];
      }
    }
  }

  /// الحصول على جميع السور (نفس getSurahs)
  Future<List<Surah>> getAllSurahs() async {
    return await getSurahs();
  }

  /// الحصول على آيات سورة معينة
  Future<List<Ayah>> getAyahsBySurah(int surahNumber) async {
    try {
      // للآن، سنعيد قائمة فارغة أو آيات تجريبية
      // يمكن تحسين هذا لاحقاً لتحميل الآيات من مصدر البيانات
      final ayahs = <Ayah>[];

      // إضافة آية تجريبية للاختبار
      ayahs.add(Ayah(
        number: 1,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        numberInSurah: 1,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
      ));

      return ayahs;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على آيات السورة $surahNumber: $e');
      return [];
    }
  }

  /// الحصول على بيانات سورة محددة
  Future<List<Ayah>> getAyahs(int surahNumber) async {
    try {
      // محاولة تحميل الآيات من ملف الأصول
      AppLogger.info('تحميل آيات السورة $surahNumber من ملف الأصول');
      final jsonString = await rootBundle.loadString(
        'assets/data/surah_$surahNumber.json',
      );
      final jsonData = json.decode(jsonString);

      // استخراج قائمة الآيات من البيانات
      final List<dynamic> ayahsJson = jsonData['data']['ayahs'];
      return ayahsJson.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على آيات السورة: $e');

      // محاولة تحميل الآيات من الإنترنت كخطة بديلة
      try {
        final response = await http
            .get(Uri.parse(
                'https://api.alquran.cloud/v1/surah/$surahNumber/ar.alafasy'))
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          final jsonData = json.decode(response.body);
          final List<dynamic> ayahsJson = jsonData['data']['ayahs'];
          return ayahsJson.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();
        }
      } catch (netError) {
        AppLogger.error('خطأ في تحميل آيات السورة من الإنترنت: $netError');
      }

      return [];
    }
  }

  /// الحصول على بيانات القرآن الكريم (من الإنترنت أو التخزين المحلي)
  Future<QuranData> _getQuranData() async {
    // التحقق من وجود بيانات مخزنة محلياً
    final prefs = await SharedPreferences.getInstance();
    final hasLocalData = prefs.containsKey(_localDataKey);
    final timestamp = prefs.getInt(_localDataTimestampKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    final isExpired = now - timestamp > _cacheExpiryDays * 24 * 60 * 60 * 1000;

    // إذا كانت البيانات موجودة ولم تنتهي صلاحيتها، نستخدمها
    if (hasLocalData && !isExpired) {
      AppLogger.info('استخدام بيانات القرآن المخزنة محلياً');
      final jsonString = prefs.getString(_localDataKey)!;
      return QuranData.fromJson(json.decode(jsonString));
    }

    // محاولة تحميل البيانات من ملف الأصول أولاً
    try {
      AppLogger.info('تحميل بيانات القرآن من ملف الأصول');
      final jsonString = await rootBundle.loadString('assets/data/quran.json');
      final jsonData = json.decode(jsonString);
      final quranData = QuranData.fromJson(jsonData);

      // تخزين البيانات محلياً
      await prefs.setString(_localDataKey, jsonString);
      await prefs.setInt(_localDataTimestampKey, now);

      AppLogger.info('تم تحميل وتخزين بيانات القرآن من ملف الأصول بنجاح');
      return quranData;
    } catch (assetError) {
      AppLogger.error('خطأ في تحميل بيانات القرآن من ملف الأصول: $assetError');

      // محاولة تحميل البيانات من الإنترنت
      try {
        AppLogger.info('محاولة تحميل بيانات القرآن من الإنترنت');
        final response = await http
            .get(Uri.parse(_apiUrl))
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          final jsonData = json.decode(response.body);
          final quranData = QuranData.fromJson(jsonData);

          // تخزين البيانات محلياً
          await prefs.setString(_localDataKey, response.body);
          await prefs.setInt(_localDataTimestampKey, now);

          AppLogger.info('تم تحميل وتخزين بيانات القرآن بنجاح');
          return quranData;
        } else {
          throw Exception('فشل في تحميل البيانات: ${response.statusCode}');
        }
      } catch (e) {
        AppLogger.error('خطأ في تحميل بيانات القرآن من الإنترنت: $e');

        // إذا كانت هناك بيانات مخزنة (حتى لو منتهية الصلاحية)، نستخدمها كخطة بديلة
        if (hasLocalData) {
          AppLogger.info('استخدام بيانات القرآن المخزنة محلياً كخطة بديلة');
          final jsonString = prefs.getString(_localDataKey)!;
          return QuranData.fromJson(json.decode(jsonString));
        }

        // إذا فشلت جميع المحاولات، نعيد بيانات فارغة
        return QuranData(surahs: []);
      }
    }
  }
}
