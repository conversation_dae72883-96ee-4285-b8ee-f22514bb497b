import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// نموذج التقييم
class FeedbackModel {
  final String id;
  final String title;
  final String content;
  final int rating;
  final DateTime createdAt;
  final bool isSubmitted;

  FeedbackModel({
    required this.id,
    required this.title,
    required this.content,
    required this.rating,
    required this.createdAt,
    this.isSubmitted = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'rating': rating,
      'createdAt': createdAt.toIso8601String(),
      'isSubmitted': isSubmitted,
    };
  }

  factory FeedbackModel.fromJson(Map<String, dynamic> json) {
    return FeedbackModel(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      rating: json['rating'],
      createdAt: DateTime.parse(json['createdAt']),
      isSubmitted: json['isSubmitted'] ?? false,
    );
  }
}

/// مزود التقييمات والملاحظات
class FeedbackProvider extends ChangeNotifier {
  List<FeedbackModel> _feedbacks = [];
  bool _isLoading = false;

  /// الحصول على قائمة التقييمات
  List<FeedbackModel> get feedbacks => _feedbacks;

  /// الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على متوسط التقييم
  double get averageRating {
    if (_feedbacks.isEmpty) return 0.0;
    
    final totalRating = _feedbacks.fold<int>(0, (sum, feedback) => sum + feedback.rating);
    return totalRating / _feedbacks.length;
  }

  /// الحصول على عدد التقييمات
  int get totalFeedbacks => _feedbacks.length;

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _loadFeedbacks();
      AppLogger.info('تم تهيئة مزود التقييمات بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود التقييمات: $e');
    }
  }

  /// تحميل التقييمات من التخزين المحلي
  Future<void> _loadFeedbacks() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      final feedbacksJson = prefs.getStringList('feedbacks') ?? [];
      
      _feedbacks = feedbacksJson.map((json) {
        final Map<String, dynamic> data = Map<String, dynamic>.from(
          Uri.splitQueryString(json),
        );
        return FeedbackModel.fromJson(data);
      }).toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      AppLogger.error('خطأ في تحميل التقييمات: $e');
    }
  }

  /// حفظ التقييمات في التخزين المحلي
  Future<void> _saveFeedbacks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final feedbacksJson = _feedbacks.map((feedback) {
        return Uri(queryParameters: feedback.toJson().map(
          (key, value) => MapEntry(key, value.toString()),
        )).query;
      }).toList();
      
      await prefs.setStringList('feedbacks', feedbacksJson);
    } catch (e) {
      AppLogger.error('خطأ في حفظ التقييمات: $e');
    }
  }

  /// إضافة تقييم جديد
  Future<void> addFeedback({
    required String title,
    required String content,
    required int rating,
  }) async {
    try {
      final feedback = FeedbackModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        content: content,
        rating: rating,
        createdAt: DateTime.now(),
      );

      _feedbacks.add(feedback);
      await _saveFeedbacks();
      notifyListeners();

      AppLogger.info('تم إضافة تقييم جديد: $title');
    } catch (e) {
      AppLogger.error('خطأ في إضافة التقييم: $e');
    }
  }

  /// حذف تقييم
  Future<void> deleteFeedback(String id) async {
    try {
      _feedbacks.removeWhere((feedback) => feedback.id == id);
      await _saveFeedbacks();
      notifyListeners();

      AppLogger.info('تم حذف التقييم: $id');
    } catch (e) {
      AppLogger.error('خطأ في حذف التقييم: $e');
    }
  }

  /// تحديث حالة الإرسال للتقييم
  Future<void> markAsSubmitted(String id) async {
    try {
      final index = _feedbacks.indexWhere((feedback) => feedback.id == id);
      if (index != -1) {
        _feedbacks[index] = FeedbackModel(
          id: _feedbacks[index].id,
          title: _feedbacks[index].title,
          content: _feedbacks[index].content,
          rating: _feedbacks[index].rating,
          createdAt: _feedbacks[index].createdAt,
          isSubmitted: true,
        );
        
        await _saveFeedbacks();
        notifyListeners();

        AppLogger.info('تم تحديث حالة التقييم كمرسل: $id');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث حالة التقييم: $e');
    }
  }

  /// الحصول على التقييمات حسب النجوم
  Map<int, int> getRatingDistribution() {
    final distribution = <int, int>{};
    
    for (int i = 1; i <= 5; i++) {
      distribution[i] = _feedbacks.where((feedback) => feedback.rating == i).length;
    }
    
    return distribution;
  }

  /// الحصول على التقييمات الأخيرة
  List<FeedbackModel> getRecentFeedbacks({int limit = 5}) {
    final sortedFeedbacks = List<FeedbackModel>.from(_feedbacks);
    sortedFeedbacks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return sortedFeedbacks.take(limit).toList();
  }

  /// البحث في التقييمات
  List<FeedbackModel> searchFeedbacks(String query) {
    if (query.isEmpty) return _feedbacks;
    
    return _feedbacks.where((feedback) {
      return feedback.title.toLowerCase().contains(query.toLowerCase()) ||
             feedback.content.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// مسح جميع التقييمات
  Future<void> clearAllFeedbacks() async {
    try {
      _feedbacks.clear();
      await _saveFeedbacks();
      notifyListeners();

      AppLogger.info('تم مسح جميع التقييمات');
    } catch (e) {
      AppLogger.error('خطأ في مسح التقييمات: $e');
    }
  }
}
