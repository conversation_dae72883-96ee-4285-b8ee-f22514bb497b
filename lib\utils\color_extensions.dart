import 'package:flutter/material.dart';

/// مساعد الألوان للتطبيق
class AppColors {
  /// إنشاء لون بشفافية محددة
  static Color primaryWithOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  /// إنشاء لون أسود بشفافية محددة
  static Color blackWithOpacity(double opacity) {
    return Colors.black.withOpacity(opacity);
  }

  /// إنشاء لون أبيض بشفافية محددة
  static Color whiteWithOpacity(double opacity) {
    return Colors.white.withOpacity(opacity);
  }

  /// إنشاء لون برتقالي بشفافية محددة
  static Color orangeWithOpacity(double opacity) {
    return Colors.orange.withOpacity(opacity);
  }

  /// إنشاء لون أخضر بشفافية محددة
  static Color greenWithOpacity(double opacity) {
    return Colors.green.withOpacity(opacity);
  }

  /// إنشاء لون أحمر بشفافية محددة
  static Color redWithOpacity(double opacity) {
    return Colors.red.withOpacity(opacity);
  }

  /// إنشاء لون أزرق بشفافية محددة
  static Color blueWithOpacity(double opacity) {
    return Colors.blue.withOpacity(opacity);
  }

  /// الألوان الأساسية للتطبيق
  static const Color primaryGreen = Color(0xFF4CAF50);
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color primaryGold = Color(0xFFFFD700);
  static const Color primaryBrown = Color(0xFF8D6E63);

  /// ألوان الخلفية الإسلامية
  static const Color islamicGold = Color(0xFFD4AF37);
  static const Color islamicGreen = Color(0xFF006633);
  static const Color islamicBlue = Color(0xFF003366);
  static const Color islamicBrown = Color(0xFF8B4513);

  /// ألوان الحالة
  static const Color successColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFF44336);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color infoColor = Color(0xFF2196F3);

  /// ألوان النص
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);

  /// ألوان الخلفية
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);

  /// الحصول على لون متدرج
  static LinearGradient getGradient(Color startColor, Color endColor) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [startColor, endColor],
    );
  }

  /// الحصول على لون متدرج إسلامي
  static LinearGradient getIslamicGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        islamicGold.withOpacity(0.8),
        islamicGreen.withOpacity(0.8),
      ],
    );
  }

  /// الحصول على لون متدرج للبطاقات
  static LinearGradient getCardGradient(bool isDark) {
    if (isDark) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          surfaceDark,
          surfaceDark.withOpacity(0.8),
        ],
      );
    } else {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          surfaceLight,
          surfaceLight.withOpacity(0.9),
        ],
      );
    }
  }

  /// الحصول على لون الظل
  static Color getShadowColor(bool isDark) {
    return isDark 
        ? Colors.black.withOpacity(0.3)
        : Colors.grey.withOpacity(0.2);
  }

  /// الحصول على لون الحدود
  static Color getBorderColor(bool isDark) {
    return isDark 
        ? Colors.white.withOpacity(0.1)
        : Colors.grey.withOpacity(0.3);
  }

  /// الحصول على لون الخلفية حسب السمة
  static Color getBackgroundColor(ThemeData theme) {
    return theme.brightness == Brightness.dark 
        ? backgroundDark 
        : backgroundLight;
  }

  /// الحصول على لون السطح حسب السمة
  static Color getSurfaceColor(ThemeData theme) {
    return theme.brightness == Brightness.dark 
        ? surfaceDark 
        : surfaceLight;
  }

  /// الحصول على لون النص الأساسي حسب السمة
  static Color getTextColor(ThemeData theme) {
    return theme.brightness == Brightness.dark 
        ? Colors.white 
        : textPrimary;
  }

  /// الحصول على لون النص الثانوي حسب السمة
  static Color getSecondaryTextColor(ThemeData theme) {
    return theme.brightness == Brightness.dark 
        ? Colors.white70 
        : textSecondary;
  }

  /// تحويل لون إلى hex
  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2)}';
  }

  /// تحويل hex إلى لون
  static Color hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }

  /// الحصول على لون متباين
  static Color getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// مزج لونين
  static Color blendColors(Color color1, Color color2, double ratio) {
    return Color.lerp(color1, color2, ratio) ?? color1;
  }

  /// الحصول على لون أفتح
  static Color lighten(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// الحصول على لون أغمق
  static Color darken(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// الحصول على لون مشبع أكثر
  static Color saturate(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final saturation = (hsl.saturation + amount).clamp(0.0, 1.0);
    return hsl.withSaturation(saturation).toColor();
  }

  /// الحصول على لون مشبع أقل
  static Color desaturate(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final saturation = (hsl.saturation - amount).clamp(0.0, 1.0);
    return hsl.withSaturation(saturation).toColor();
  }
}
