import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/theme_provider.dart';
import '../services/daily_ayah_provider.dart';
import '../services/prayer_provider.dart';
import '../services/daily_question_service.dart';
import '../utils/logger.dart';

/// الشاشة الرئيسية للتطبيق
class MainScreen extends StatefulWidget {
  final bool showDailyAyah;

  const MainScreen({
    super.key,
    this.showDailyAyah = false,
  });

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    AppLogger.info('تم تهيئة الشاشة الرئيسية');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أذكاري'),
        centerTitle: true,
        elevation: 0,
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    switch (_currentIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildQuranTab();
      case 2:
        return _buildPrayerTab();
      case 3:
        return _buildMoreTab();
      default:
        return _buildHomeTab();
    }
  }

  /// بناء تبويب الرئيسية
  Widget _buildHomeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة الآية اليومية
          _buildDailyAyahCard(),
          const SizedBox(height: 16),
          
          // بطاقة أوقات الصلاة
          _buildPrayerTimesCard(),
          const SizedBox(height: 16),
          
          // بطاقة الأسئلة اليومية
          _buildDailyQuestionsCard(),
          const SizedBox(height: 16),
          
          // الأذكار اليومية
          _buildDailyAzkarSection(),
        ],
      ),
    );
  }

  /// بناء بطاقة الآية اليومية
  Widget _buildDailyAyahCard() {
    return Consumer<DailyAyahProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        if (!provider.hasDailyAyah) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('لا توجد آية يومية متاحة'),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'آية اليوم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  provider.getFormattedAyahText(),
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      provider.getSurahInfo(),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: () => provider.refreshDailyAyah(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء بطاقة أوقات الصلاة
  Widget _buildPrayerTimesCard() {
    return Consumer<PrayerProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'أوقات الصلاة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                if (provider.currentPrayerTimes != null) ...[
                  Text('الصلاة التالية: ${provider.getNextPrayer()}'),
                  const SizedBox(height: 4),
                  Text('الوقت المتبقي: ${_formatDuration(provider.getTimeToNextPrayer())}'),
                ] else ...[
                  const Text('لا توجد أوقات صلاة متاحة'),
                ],
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => provider.refreshPrayerTimes(),
                  child: const Text('تحديث أوقات الصلاة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء بطاقة الأسئلة اليومية
  Widget _buildDailyQuestionsCard() {
    return Consumer<DailyQuestionService>(
      builder: (context, service, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'الأسئلة اليومية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                if (service.isAllQuestionsAnswered) ...[
                  Text('تم الانتهاء من جميع الأسئلة!'),
                  Text('النتيجة: ${service.correctAnswersCount}/${service.todayQuestions.length}'),
                ] else ...[
                  Text('السؤال ${service.currentQuestionIndex + 1} من ${service.todayQuestions.length}'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      // التنقل إلى شاشة الأسئلة
                    },
                    child: const Text('ابدأ الأسئلة'),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم الأذكار اليومية
  Widget _buildDailyAzkarSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الأذكار اليومية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.5,
          children: [
            _buildAzkarCard('أذكار الصباح', Icons.wb_sunny),
            _buildAzkarCard('أذكار المساء', Icons.nights_stay),
            _buildAzkarCard('أذكار النوم', Icons.bedtime),
            _buildAzkarCard('أذكار الطعام', Icons.restaurant),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة ذكر
  Widget _buildAzkarCard(String title, IconData icon) {
    return Card(
      child: InkWell(
        onTap: () {
          // التنقل إلى شاشة الذكر
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء تبويب القرآن
  Widget _buildQuranTab() {
    return const Center(
      child: Text('تبويب القرآن الكريم'),
    );
  }

  /// بناء تبويب الصلاة
  Widget _buildPrayerTab() {
    return const Center(
      child: Text('تبويب أوقات الصلاة'),
    );
  }

  /// بناء تبويب المزيد
  Widget _buildMoreTab() {
    return const Center(
      child: Text('تبويب المزيد'),
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.book),
          label: 'القرآن',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.access_time),
          label: 'الصلاة',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.more_horiz),
          label: 'المزيد',
        ),
      ],
    );
  }

  /// تنسيق المدة الزمنية
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }
}
