import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_model.dart';
import 'quran_service.dart';
import '../utils/logger.dart';
import 'dart:math';

/// نموذج الآية اليومية
class DailyAyah {
  final Surah surah;
  final Ayah ayah;
  final DateTime date;

  DailyAyah({
    required this.surah,
    required this.ayah,
    required this.date,
  });

  Map<String, dynamic> toJson() {
    return {
      'surah': surah.toJson(),
      'ayah': ayah.toJson(),
      'date': date.toIso8601String(),
    };
  }

  factory DailyAyah.fromJson(Map<String, dynamic> json) {
    return DailyAyah(
      surah: Surah.fromJson(json['surah']),
      ayah: Ayah.fromJson(json['ayah']),
      date: DateTime.parse(json['date']),
    );
  }
}

/// مزود الآية اليومية
class DailyAyahProvider extends ChangeNotifier {
  DailyAyah? _currentDailyAyah;
  bool _isLoading = false;
  final QuranService _quranService = QuranService();

  /// الحصول على الآية اليومية الحالية
  DailyAyah? get currentDailyAyah => _currentDailyAyah;

  /// الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _loadDailyAyah();
      AppLogger.info('تم تهيئة مزود الآية اليومية بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود الآية اليومية: $e');
    }
  }

  /// تحميل الآية اليومية
  Future<void> _loadDailyAyah() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';

      // التحقق من وجود آية محفوظة لليوم الحالي
      final savedAyahJson = prefs.getString('daily_ayah_$todayString');

      if (savedAyahJson != null) {
        // تحميل الآية المحفوظة
        final Map<String, dynamic> ayahData = Map<String, dynamic>.from(
          Uri.splitQueryString(savedAyahJson),
        );
        _currentDailyAyah = DailyAyah.fromJson(ayahData);
      } else {
        // إنشاء آية جديدة لليوم
        await _generateNewDailyAyah();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      AppLogger.error('خطأ في تحميل الآية اليومية: $e');
    }
  }

  /// إنشاء آية يومية جديدة
  Future<void> _generateNewDailyAyah() async {
    try {
      // الحصول على قائمة السور
      final surahs = await _quranService.getAllSurahs();

      if (surahs.isNotEmpty) {
        // اختيار سورة عشوائية
        final random = Random();
        final randomSurah = surahs[random.nextInt(surahs.length)];

        // الحصول على آيات السورة
        final ayahs = await _quranService.getAyahsBySurah(randomSurah.number);

        if (ayahs.isNotEmpty) {
          // اختيار آية عشوائية
          final randomAyah = ayahs[random.nextInt(ayahs.length)];

          // إنشاء الآية اليومية
          _currentDailyAyah = DailyAyah(
            surah: randomSurah,
            ayah: randomAyah,
            date: DateTime.now(),
          );

          // حفظ الآية اليومية
          await _saveDailyAyah();

          AppLogger.info(
              'تم إنشاء آية يومية جديدة: ${randomSurah.name} - آية ${randomAyah.numberInSurah}');
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في إنشاء آية يومية جديدة: $e');
    }
  }

  /// حفظ الآية اليومية
  Future<void> _saveDailyAyah() async {
    try {
      if (_currentDailyAyah != null) {
        final prefs = await SharedPreferences.getInstance();
        final today = DateTime.now();
        final todayString = '${today.year}-${today.month}-${today.day}';

        final ayahJson = Uri(
            queryParameters: _currentDailyAyah!.toJson().map(
                  (key, value) => MapEntry(key, value.toString()),
                )).query;

        await prefs.setString('daily_ayah_$todayString', ayahJson);
      }
    } catch (e) {
      AppLogger.error('خطأ في حفظ الآية اليومية: $e');
    }
  }

  /// تحديث الآية اليومية يدوياً
  Future<void> refreshDailyAyah() async {
    try {
      _isLoading = true;
      notifyListeners();

      await _generateNewDailyAyah();

      _isLoading = false;
      notifyListeners();

      AppLogger.info('تم تحديث الآية اليومية يدوياً');
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      AppLogger.error('خطأ في تحديث الآية اليومية: $e');
    }
  }

  /// الحصول على نص الآية مع اسم السورة
  String getFormattedAyahText() {
    if (_currentDailyAyah == null) return '';

    return '${_currentDailyAyah!.ayah.text}\n\n'
        '﴿ ${_currentDailyAyah!.surah.name} - آية ${_currentDailyAyah!.ayah.numberInSurah} ﴾';
  }

  /// الحصول على معلومات السورة
  String getSurahInfo() {
    if (_currentDailyAyah == null) return '';

    return '${_currentDailyAyah!.surah.name} - ${_currentDailyAyah!.surah.revelationType == "Meccan" ? "مكية" : "مدنية"}';
  }

  /// التحقق من وجود آية يومية
  bool get hasDailyAyah => _currentDailyAyah != null;

  /// مشاركة الآية اليومية
  String getShareText() {
    if (_currentDailyAyah == null) return '';

    return '${_currentDailyAyah!.ayah.text}\n\n'
        '﴿ ${_currentDailyAyah!.surah.name} - آية ${_currentDailyAyah!.ayah.numberInSurah} ﴾\n\n'
        'من تطبيق أذكاري';
  }

  /// الحصول على تاريخ الآية
  String getAyahDate() {
    if (_currentDailyAyah == null) return '';

    final date = _currentDailyAyah!.date;
    return '${date.day}/${date.month}/${date.year}';
  }

  /// مسح الآية اليومية المحفوظة
  Future<void> clearSavedAyah() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';

      await prefs.remove('daily_ayah_$todayString');
      _currentDailyAyah = null;
      notifyListeners();

      AppLogger.info('تم مسح الآية اليومية المحفوظة');
    } catch (e) {
      AppLogger.error('خطأ في مسح الآية اليومية: $e');
    }
  }
}
