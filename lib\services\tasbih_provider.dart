import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:vibration/vibration.dart';
import '../utils/platform_helper.dart';
import '../utils/logger.dart';

/// مزود التسبيح - يدير عداد التسبيح والتفاعل معه
class TasbihProvider extends ChangeNotifier {
  // العداد الحالي
  int _count = 0;
  
  // الهدف المحدد
  int _target = 33;
  
  // حالة تشغيل الصوت
  bool _isSoundEnabled = true;
  
  // حالة الاهتزاز
  bool _isVibrationEnabled = true;
  
  // مشغل الصوت
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // قائمة الأهداف المتاحة
  final List<int> _availableTargets = [33, 99, 100, 1000];

  /// الحصول على العداد الحالي
  int get count => _count;

  /// الحصول على الهدف المحدد
  int get target => _target;

  /// الحصول على حالة تشغيل الصوت
  bool get isSoundEnabled => _isSoundEnabled;

  /// الحصول على حالة الاهتزاز
  bool get isVibrationEnabled => _isVibrationEnabled;

  /// الحصول على قائمة الأهداف المتاحة
  List<int> get availableTargets => _availableTargets;

  /// الحصول على النسبة المئوية للتقدم
  double get progress => _target > 0 ? (_count / _target).clamp(0.0, 1.0) : 0.0;

  /// التحقق من الوصول للهدف
  bool get isTargetReached => _count >= _target;

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      await _loadSettings();
      AppLogger.info('تم تهيئة مزود التسبيح بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود التسبيح: $e');
    }
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _count = prefs.getInt('tasbih_count') ?? 0;
      _target = prefs.getInt('tasbih_target') ?? 33;
      _isSoundEnabled = prefs.getBool('tasbih_sound_enabled') ?? true;
      _isVibrationEnabled = prefs.getBool('tasbih_vibration_enabled') ?? true;
      
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل إعدادات التسبيح: $e');
    }
  }

  /// حفظ الإعدادات في التخزين المحلي
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setInt('tasbih_count', _count);
      await prefs.setInt('tasbih_target', _target);
      await prefs.setBool('tasbih_sound_enabled', _isSoundEnabled);
      await prefs.setBool('tasbih_vibration_enabled', _isVibrationEnabled);
    } catch (e) {
      AppLogger.error('خطأ في حفظ إعدادات التسبيح: $e');
    }
  }

  /// زيادة العداد
  Future<void> increment() async {
    _count++;
    await _saveSettings();
    
    // تشغيل الصوت إذا كان مفعلاً
    if (_isSoundEnabled) {
      await _playSound();
    }
    
    // تشغيل الاهتزاز إذا كان مفعلاً
    if (_isVibrationEnabled && PlatformHelper.supportsVibration) {
      await _vibrate();
    }
    
    notifyListeners();
    
    // التحقق من الوصول للهدف
    if (isTargetReached && _count == _target) {
      await _onTargetReached();
    }
    
    AppLogger.info('تم زيادة عداد التسبيح إلى: $_count');
  }

  /// إعادة تعيين العداد
  Future<void> reset() async {
    _count = 0;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم إعادة تعيين عداد التسبيح');
  }

  /// تعيين الهدف
  Future<void> setTarget(int newTarget) async {
    if (newTarget > 0) {
      _target = newTarget;
      await _saveSettings();
      notifyListeners();
      AppLogger.info('تم تعيين هدف التسبيح إلى: $_target');
    }
  }

  /// تبديل حالة الصوت
  Future<void> toggleSound() async {
    _isSoundEnabled = !_isSoundEnabled;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم تبديل حالة صوت التسبيح: $_isSoundEnabled');
  }

  /// تبديل حالة الاهتزاز
  Future<void> toggleVibration() async {
    _isVibrationEnabled = !_isVibrationEnabled;
    await _saveSettings();
    notifyListeners();
    AppLogger.info('تم تبديل حالة اهتزاز التسبيح: $_isVibrationEnabled');
  }

  /// تشغيل صوت النقر
  Future<void> _playSound() async {
    try {
      // يمكن إضافة ملف صوتي مخصص هنا
      // await _audioPlayer.play(AssetSource('sounds/tasbih_click.mp3'));
    } catch (e) {
      AppLogger.error('خطأ في تشغيل صوت التسبيح: $e');
    }
  }

  /// تشغيل الاهتزاز
  Future<void> _vibrate() async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        await Vibration.vibrate(duration: 50);
      }
    } catch (e) {
      AppLogger.error('خطأ في تشغيل اهتزاز التسبيح: $e');
    }
  }

  /// معالج الوصول للهدف
  Future<void> _onTargetReached() async {
    try {
      // اهتزاز أطول عند الوصول للهدف
      if (_isVibrationEnabled && PlatformHelper.supportsVibration) {
        if (await Vibration.hasVibrator() ?? false) {
          await Vibration.vibrate(duration: 200);
        }
      }
      
      // يمكن إضافة صوت مختلف عند الوصول للهدف
      if (_isSoundEnabled) {
        // await _audioPlayer.play(AssetSource('sounds/target_reached.mp3'));
      }
      
      AppLogger.info('تم الوصول لهدف التسبيح: $_target');
    } catch (e) {
      AppLogger.error('خطأ في معالج الوصول للهدف: $e');
    }
  }

  /// الحصول على النص المناسب للعداد
  String getCountText() {
    return _count.toString();
  }

  /// الحصول على النص المناسب للهدف
  String getTargetText() {
    return 'الهدف: $_target';
  }

  /// الحصول على النص المناسب للتقدم
  String getProgressText() {
    return '${(_count / _target * 100).toInt()}%';
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
