import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

/// مساعد للتحقق من نوع المنصة
class PlatformHelper {
  /// التحقق من أن المنصة هي Android
  static bool get isAndroid {
    try {
      return !kIsWeb && Platform.isAndroid;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من أن المنصة هي iOS
  static bool get isIOS {
    try {
      return !kIsWeb && Platform.isIOS;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من أن المنصة هي Windows
  static bool get isWindows {
    try {
      return !kIsWeb && Platform.isWindows;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من أن المنصة هي macOS
  static bool get isMacOS {
    try {
      return !kIsWeb && Platform.isMacOS;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من أن المنصة هي Linux
  static bool get isLinux {
    try {
      return !kIsWeb && Platform.isLinux;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من أن المنصة هي Web
  static bool get isWeb {
    return kIsWeb;
  }

  /// التحقق من أن المنصة هي منصة محمولة (Android أو iOS)
  static bool get isMobile {
    return isAndroid || isIOS;
  }

  /// التحقق من أن المنصة هي منصة سطح مكتب (Windows أو macOS أو Linux)
  static bool get isDesktop {
    return isWindows || isMacOS || isLinux;
  }

  /// الحصول على اسم المنصة
  static String get platformName {
    if (isWeb) return 'Web';
    if (isAndroid) return 'Android';
    if (isIOS) return 'iOS';
    if (isWindows) return 'Windows';
    if (isMacOS) return 'macOS';
    if (isLinux) return 'Linux';
    return 'Unknown';
  }

  /// الحصول على نسخة نظام التشغيل
  static String get operatingSystemVersion {
    try {
      if (kIsWeb) return 'Web';
      return Platform.operatingSystemVersion;
    } catch (e) {
      return 'Unknown';
    }
  }

  /// التحقق من دعم الإشعارات
  static bool get supportsNotifications {
    return isMobile || isDesktop;
  }

  /// التحقق من دعم الاهتزاز
  static bool get supportsVibration {
    return isMobile;
  }

  /// التحقق من دعم تحديد الموقع
  static bool get supportsLocation {
    return isMobile || isWeb;
  }
}
