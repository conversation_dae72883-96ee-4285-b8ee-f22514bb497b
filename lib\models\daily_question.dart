/// نموذج السؤال اليومي
class DailyQuestion {
  final int id;
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final String explanation;
  final String category;
  final DateTime date;
  final String? source;
  final int difficulty; // 1-3 (سهل، متوسط، صعب)

  DailyQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    required this.explanation,
    required this.category,
    required this.date,
    this.source,
    this.difficulty = 1,
  });

  factory DailyQuestion.fromJson(Map<String, dynamic> json) {
    return DailyQuestion(
      id: json['id'] ?? 0,
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      correctAnswerIndex: json['correct_answer_index'] ?? 0,
      explanation: json['explanation'] ?? '',
      category: json['category'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      source: json['source'],
      difficulty: json['difficulty'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correct_answer_index': correctAnswerIndex,
      'explanation': explanation,
      'category': category,
      'date': date.toIso8601String(),
      'source': source,
      'difficulty': difficulty,
    };
  }

  /// الحصول على نص مستوى الصعوبة
  String get difficultyText {
    switch (difficulty) {
      case 1:
        return 'سهل';
      case 2:
        return 'متوسط';
      case 3:
        return 'صعب';
      default:
        return 'سهل';
    }
  }

  /// الحصول على لون مستوى الصعوبة
  String get difficultyColor {
    switch (difficulty) {
      case 1:
        return '#4CAF50'; // أخضر
      case 2:
        return '#FF9800'; // برتقالي
      case 3:
        return '#F44336'; // أحمر
      default:
        return '#4CAF50';
    }
  }
}

/// نموذج إجابة المستخدم
class UserAnswer {
  final int questionId;
  final int selectedAnswerIndex;
  final bool isCorrect;
  final DateTime answeredAt;
  final int timeSpentSeconds;

  UserAnswer({
    required this.questionId,
    required this.selectedAnswerIndex,
    required this.isCorrect,
    required this.answeredAt,
    this.timeSpentSeconds = 0,
  });

  factory UserAnswer.fromJson(Map<String, dynamic> json) {
    return UserAnswer(
      questionId: json['question_id'] ?? 0,
      selectedAnswerIndex: json['selected_answer_index'] ?? 0,
      isCorrect: json['is_correct'] ?? false,
      answeredAt: DateTime.parse(json['answered_at'] ?? DateTime.now().toIso8601String()),
      timeSpentSeconds: json['time_spent_seconds'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question_id': questionId,
      'selected_answer_index': selectedAnswerIndex,
      'is_correct': isCorrect,
      'answered_at': answeredAt.toIso8601String(),
      'time_spent_seconds': timeSpentSeconds,
    };
  }
}

/// نموذج إحصائيات الأسئلة
class QuestionStats {
  final int totalQuestions;
  final int correctAnswers;
  final int wrongAnswers;
  final int totalTimeSpent;
  final double averageTimePerQuestion;
  final Map<String, int> categoryStats;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastAnswered;

  QuestionStats({
    required this.totalQuestions,
    required this.correctAnswers,
    required this.wrongAnswers,
    required this.totalTimeSpent,
    required this.averageTimePerQuestion,
    required this.categoryStats,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastAnswered,
  });

  factory QuestionStats.fromJson(Map<String, dynamic> json) {
    return QuestionStats(
      totalQuestions: json['total_questions'] ?? 0,
      correctAnswers: json['correct_answers'] ?? 0,
      wrongAnswers: json['wrong_answers'] ?? 0,
      totalTimeSpent: json['total_time_spent'] ?? 0,
      averageTimePerQuestion: (json['average_time_per_question'] ?? 0.0).toDouble(),
      categoryStats: Map<String, int>.from(json['category_stats'] ?? {}),
      currentStreak: json['current_streak'] ?? 0,
      longestStreak: json['longest_streak'] ?? 0,
      lastAnswered: DateTime.parse(json['last_answered'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_questions': totalQuestions,
      'correct_answers': correctAnswers,
      'wrong_answers': wrongAnswers,
      'total_time_spent': totalTimeSpent,
      'average_time_per_question': averageTimePerQuestion,
      'category_stats': categoryStats,
      'current_streak': currentStreak,
      'longest_streak': longestStreak,
      'last_answered': lastAnswered.toIso8601String(),
    };
  }

  /// النسبة المئوية للإجابات الصحيحة
  double get correctPercentage {
    return totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0.0;
  }

  /// النسبة المئوية للإجابات الخاطئة
  double get wrongPercentage {
    return totalQuestions > 0 ? (wrongAnswers / totalQuestions) * 100 : 0.0;
  }

  /// متوسط الوقت بالثواني
  double get averageTimeInSeconds {
    return totalQuestions > 0 ? totalTimeSpent / totalQuestions : 0.0;
  }

  /// تقييم الأداء
  String get performanceRating {
    final percentage = correctPercentage;
    if (percentage >= 90) return 'ممتاز';
    if (percentage >= 80) return 'جيد جداً';
    if (percentage >= 70) return 'جيد';
    if (percentage >= 60) return 'مقبول';
    return 'يحتاج تحسين';
  }

  /// لون تقييم الأداء
  String get performanceColor {
    final percentage = correctPercentage;
    if (percentage >= 90) return '#4CAF50'; // أخضر
    if (percentage >= 80) return '#8BC34A'; // أخضر فاتح
    if (percentage >= 70) return '#FFEB3B'; // أصفر
    if (percentage >= 60) return '#FF9800'; // برتقالي
    return '#F44336'; // أحمر
  }
}

/// نموذج جلسة الأسئلة اليومية
class DailyQuestionSession {
  final DateTime date;
  final List<DailyQuestion> questions;
  final List<UserAnswer> answers;
  final bool isCompleted;
  final DateTime? completedAt;
  final int totalTimeSpent;

  DailyQuestionSession({
    required this.date,
    required this.questions,
    required this.answers,
    required this.isCompleted,
    this.completedAt,
    this.totalTimeSpent = 0,
  });

  factory DailyQuestionSession.fromJson(Map<String, dynamic> json) {
    return DailyQuestionSession(
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      questions: (json['questions'] as List<dynamic>?)
          ?.map((q) => DailyQuestion.fromJson(q))
          .toList() ?? [],
      answers: (json['answers'] as List<dynamic>?)
          ?.map((a) => UserAnswer.fromJson(a))
          .toList() ?? [],
      isCompleted: json['is_completed'] ?? false,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at'])
          : null,
      totalTimeSpent: json['total_time_spent'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'questions': questions.map((q) => q.toJson()).toList(),
      'answers': answers.map((a) => a.toJson()).toList(),
      'is_completed': isCompleted,
      'completed_at': completedAt?.toIso8601String(),
      'total_time_spent': totalTimeSpent,
    };
  }

  /// عدد الإجابات الصحيحة
  int get correctAnswersCount {
    return answers.where((answer) => answer.isCorrect).length;
  }

  /// النسبة المئوية للنجاح
  double get successPercentage {
    return questions.isNotEmpty ? (correctAnswersCount / questions.length) * 100 : 0.0;
  }

  /// التحقق من إمكانية البدء
  bool get canStart => !isCompleted && answers.length < questions.length;

  /// السؤال الحالي
  DailyQuestion? get currentQuestion {
    if (answers.length < questions.length) {
      return questions[answers.length];
    }
    return null;
  }

  /// فهرس السؤال الحالي
  int get currentQuestionIndex => answers.length;
}
