/// نموذج التصنيف
class Category {
  final int id;
  final String name;
  final String description;
  final String iconName;
  final int azkarCount;

  Category({
    required this.id,
    required this.name,
    required this.description,
    required this.iconName,
    required this.azkarCount,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      iconName: json['icon_name'] ?? '',
      azkarCount: json['azkar_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon_name': iconName,
      'azkar_count': azkarCount,
    };
  }
}

/// نموذج الذكر
class Zikr {
  final int id;
  final String text;
  final String source;
  final int count;
  final int categoryId;
  final String? translation;
  final String? benefits;
  final bool isFavorite;
  final int currentCount;

  Zikr({
    required this.id,
    required this.text,
    required this.source,
    required this.count,
    required this.categoryId,
    this.translation,
    this.benefits,
    this.isFavorite = false,
    this.currentCount = 0,
  });

  factory Zikr.fromJson(Map<String, dynamic> json) {
    return Zikr(
      id: json['id'] ?? 0,
      text: json['text'] ?? '',
      source: json['source'] ?? '',
      count: json['count'] ?? 1,
      categoryId: json['category_id'] ?? 0,
      translation: json['translation'],
      benefits: json['benefits'],
      isFavorite: json['is_favorite'] ?? false,
      currentCount: json['current_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'source': source,
      'count': count,
      'category_id': categoryId,
      'translation': translation,
      'benefits': benefits,
      'is_favorite': isFavorite,
      'current_count': currentCount,
    };
  }

  /// إنشاء نسخة جديدة مع تحديث بعض الخصائص
  Zikr copyWith({
    int? id,
    String? text,
    String? source,
    int? count,
    int? categoryId,
    String? translation,
    String? benefits,
    bool? isFavorite,
    int? currentCount,
  }) {
    return Zikr(
      id: id ?? this.id,
      text: text ?? this.text,
      source: source ?? this.source,
      count: count ?? this.count,
      categoryId: categoryId ?? this.categoryId,
      translation: translation ?? this.translation,
      benefits: benefits ?? this.benefits,
      isFavorite: isFavorite ?? this.isFavorite,
      currentCount: currentCount ?? this.currentCount,
    );
  }

  /// التحقق من اكتمال العد
  bool get isCompleted => currentCount >= count;

  /// النسبة المئوية للتقدم
  double get progress => count > 0 ? (currentCount / count).clamp(0.0, 1.0) : 0.0;
}

/// نموذج الذكر المخصص
class CustomZikr {
  final int id;
  final String text;
  final String title;
  final int count;
  final DateTime createdAt;
  final bool isFavorite;
  final int currentCount;

  CustomZikr({
    required this.id,
    required this.text,
    required this.title,
    required this.count,
    required this.createdAt,
    this.isFavorite = false,
    this.currentCount = 0,
  });

  factory CustomZikr.fromJson(Map<String, dynamic> json) {
    return CustomZikr(
      id: json['id'] ?? 0,
      text: json['text'] ?? '',
      title: json['title'] ?? '',
      count: json['count'] ?? 1,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      isFavorite: json['is_favorite'] ?? false,
      currentCount: json['current_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'title': title,
      'count': count,
      'created_at': createdAt.toIso8601String(),
      'is_favorite': isFavorite,
      'current_count': currentCount,
    };
  }

  /// إنشاء نسخة جديدة مع تحديث بعض الخصائص
  CustomZikr copyWith({
    int? id,
    String? text,
    String? title,
    int? count,
    DateTime? createdAt,
    bool? isFavorite,
    int? currentCount,
  }) {
    return CustomZikr(
      id: id ?? this.id,
      text: text ?? this.text,
      title: title ?? this.title,
      count: count ?? this.count,
      createdAt: createdAt ?? this.createdAt,
      isFavorite: isFavorite ?? this.isFavorite,
      currentCount: currentCount ?? this.currentCount,
    );
  }

  /// التحقق من اكتمال العد
  bool get isCompleted => currentCount >= count;

  /// النسبة المئوية للتقدم
  double get progress => count > 0 ? (currentCount / count).clamp(0.0, 1.0) : 0.0;
}

/// نموذج إحصائيات الأذكار
class AzkarStats {
  final int totalAzkar;
  final int completedAzkar;
  final int favoriteAzkar;
  final int customAzkar;
  final DateTime lastUpdated;

  AzkarStats({
    required this.totalAzkar,
    required this.completedAzkar,
    required this.favoriteAzkar,
    required this.customAzkar,
    required this.lastUpdated,
  });

  factory AzkarStats.fromJson(Map<String, dynamic> json) {
    return AzkarStats(
      totalAzkar: json['total_azkar'] ?? 0,
      completedAzkar: json['completed_azkar'] ?? 0,
      favoriteAzkar: json['favorite_azkar'] ?? 0,
      customAzkar: json['custom_azkar'] ?? 0,
      lastUpdated: DateTime.parse(json['last_updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_azkar': totalAzkar,
      'completed_azkar': completedAzkar,
      'favorite_azkar': favoriteAzkar,
      'custom_azkar': customAzkar,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// النسبة المئوية للإكمال
  double get completionPercentage {
    return totalAzkar > 0 ? (completedAzkar / totalAzkar) * 100 : 0.0;
  }
}
