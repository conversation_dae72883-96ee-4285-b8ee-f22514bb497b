import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;
import '../utils/platform_helper.dart';
import '../utils/logger.dart';

/// خدمة الإشعارات - تدير إرسال وجدولة الإشعارات
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  Future<void> init() async {
    if (_isInitialized) return;

    try {
      // التحقق من دعم المنصة للإشعارات
      if (!PlatformHelper.supportsNotifications) {
        AppLogger.info('المنصة الحالية لا تدعم الإشعارات');
        return;
      }

      // طلب الأذونات
      await _requestPermissions();

      // إعدادات Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // إعدادات التهيئة العامة
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // تهيئة المكون الإضافي
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _isInitialized = true;
      AppLogger.info('تم تهيئة خدمة الإشعارات بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    try {
      if (PlatformHelper.isAndroid) {
        // طلب إذن الإشعارات لـ Android 13+
        await Permission.notification.request();
      }
    } catch (e) {
      AppLogger.error('خطأ في طلب أذونات الإشعارات: $e');
    }
  }

  /// معالج النقر على الإشعار
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    AppLogger.info('تم النقر على الإشعار: ${notificationResponse.payload}');
    // يمكن إضافة منطق إضافي هنا للتنقل أو تنفيذ إجراءات معينة
  }

  /// إرسال إشعار فوري
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) {
      AppLogger.warning('خدمة الإشعارات غير مهيئة');
      return;
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'azkary_channel',
        'أذكاري',
        channelDescription: 'إشعارات تطبيق أذكاري',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails();

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      AppLogger.info('تم إرسال الإشعار: $title');
    } catch (e) {
      AppLogger.error('خطأ في إرسال الإشعار: $e');
    }
  }

  /// جدولة إشعار
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_isInitialized) {
      AppLogger.warning('خدمة الإشعارات غير مهيئة');
      return;
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'azkary_scheduled_channel',
        'أذكاري - مجدولة',
        channelDescription: 'إشعارات مجدولة لتطبيق أذكاري',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails();

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // تحويل DateTime إلى TZDateTime
      final tz.TZDateTime tzScheduledDate = tz.TZDateTime.from(
        scheduledDate,
        tz.local,
      );

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledDate,
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        payload: payload,
      );

      AppLogger.info('تم جدولة الإشعار: $title في $scheduledDate');
    } catch (e) {
      AppLogger.error('خطأ في جدولة الإشعار: $e');
    }
  }

  /// إلغاء إشعار محدد
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
      AppLogger.info('تم إلغاء الإشعار رقم: $id');
    } catch (e) {
      AppLogger.error('خطأ في إلغاء الإشعار: $e');
    }
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      AppLogger.info('تم إلغاء جميع الإشعارات');
    } catch (e) {
      AppLogger.error('خطأ في إلغاء جميع الإشعارات: $e');
    }
  }

  /// إرسال إشعار تذكير بالأذكار
  Future<void> showZikrReminder(String zikrText) async {
    await showNotification(
      id: 1,
      title: 'تذكير بالأذكار',
      body: zikrText,
      payload: 'zikr_reminder',
    );
  }

  /// إرسال إشعار تذكير بالصلاة
  Future<void> showPrayerReminder(String prayerName) async {
    await showNotification(
      id: 2,
      title: 'تذكير بالصلاة',
      body: 'حان وقت صلاة $prayerName',
      payload: 'prayer_reminder',
    );
  }
}
