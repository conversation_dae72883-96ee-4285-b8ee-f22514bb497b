import 'package:flutter/material.dart';

/// أيقونة السؤال المتحركة
class AnimatedQuestionIcon extends StatefulWidget {
  final bool isAnswered;
  final double size;
  final VoidCallback? onTap;
  final bool showMiniVersion;

  const AnimatedQuestionIcon({
    super.key,
    required this.isAnswered,
    this.size = 50,
    this.onTap,
    this.showMiniVersion = false,
  });

  @override
  State<AnimatedQuestionIcon> createState() => _AnimatedQuestionIconState();
}

class _AnimatedQuestionIconState extends State<AnimatedQuestionIcon>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    
    // تحكم في النبضة
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // تحكم في الدوران
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // بدء الحركة إذا لم يتم الإجابة
    if (!widget.isAnswered) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
  }

  void _stopAnimations() {
    _pulseController.stop();
    _rotationController.stop();
  }

  @override
  void didUpdateWidget(AnimatedQuestionIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.isAnswered != widget.isAnswered) {
      if (widget.isAnswered) {
        _stopAnimations();
      } else {
        _startAnimations();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final iconSize = widget.showMiniVersion ? widget.size * 0.6 : widget.size;
    
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _rotationAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: widget.isAnswered ? 1.0 : _pulseAnimation.value,
            child: Transform.rotate(
              angle: widget.isAnswered ? 0.0 : _rotationAnimation.value * 2 * 3.14159,
              child: Container(
                width: iconSize,
                height: iconSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: widget.isAnswered
                      ? LinearGradient(
                          colors: [
                            Colors.green.shade400,
                            Colors.green.shade600,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : LinearGradient(
                          colors: [
                            theme.primaryColor.withOpacity(0.8),
                            theme.primaryColor,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                  boxShadow: [
                    BoxShadow(
                      color: (widget.isAnswered ? Colors.green : theme.primaryColor)
                          .withOpacity(0.3),
                      blurRadius: widget.isAnswered ? 8 : 12,
                      spreadRadius: widget.isAnswered ? 2 : 4,
                    ),
                  ],
                ),
                child: Icon(
                  widget.isAnswered ? Icons.check : Icons.quiz,
                  color: Colors.white,
                  size: iconSize * 0.5,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// أيقونة السؤال البسيطة (بدون حركة)
class SimpleQuestionIcon extends StatelessWidget {
  final bool isAnswered;
  final double size;
  final VoidCallback? onTap;

  const SimpleQuestionIcon({
    super.key,
    required this.isAnswered,
    this.size = 50,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isAnswered ? Colors.green : theme.primaryColor,
          boxShadow: [
            BoxShadow(
              color: (isAnswered ? Colors.green : theme.primaryColor)
                  .withOpacity(0.3),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Icon(
          isAnswered ? Icons.check : Icons.quiz,
          color: Colors.white,
          size: size * 0.5,
        ),
      ),
    );
  }
}

/// مؤشر تقدم الأسئلة
class QuestionProgressIndicator extends StatelessWidget {
  final int answeredQuestions;
  final int totalQuestions;
  final double size;

  const QuestionProgressIndicator({
    super.key,
    required this.answeredQuestions,
    required this.totalQuestions,
    this.size = 60,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progress = totalQuestions > 0 ? answeredQuestions / totalQuestions : 0.0;
    
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        children: [
          // الدائرة الخلفية
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.surface,
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.3),
                width: 2,
              ),
            ),
          ),
          
          // مؤشر التقدم الدائري
          CircularProgressIndicator(
            value: progress,
            strokeWidth: 4,
            backgroundColor: Colors.transparent,
            valueColor: AlwaysStoppedAnimation<Color>(
              progress == 1.0 ? Colors.green : theme.primaryColor,
            ),
          ),
          
          // النص في المنتصف
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  progress == 1.0 ? Icons.check : Icons.quiz,
                  color: progress == 1.0 ? Colors.green : theme.primaryColor,
                  size: size * 0.3,
                ),
                if (size > 40)
                  Text(
                    '$answeredQuestions/$totalQuestions',
                    style: TextStyle(
                      fontSize: size * 0.15,
                      fontWeight: FontWeight.bold,
                      color: progress == 1.0 ? Colors.green : theme.primaryColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// شارة الأسئلة (للإشعارات)
class QuestionBadge extends StatelessWidget {
  final int count;
  final Widget child;

  const QuestionBadge({
    super.key,
    required this.count,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (count > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                count.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}
