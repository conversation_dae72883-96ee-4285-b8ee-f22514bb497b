import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/daily_question_service.dart';
import '../models/daily_question.dart';
import '../widgets/islamic_background.dart';

/// شاشة الأسئلة اليومية
class DailyQuestionScreen extends StatefulWidget {
  const DailyQuestionScreen({super.key});

  @override
  State<DailyQuestionScreen> createState() => _DailyQuestionScreenState();
}

class _DailyQuestionScreenState extends State<DailyQuestionScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  int? _selectedAnswerIndex;
  bool _showResult = false;
  bool _isAnswerCorrect = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأسئلة اليومية'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Consumer<DailyQuestionService>(
          builder: (context, questionService, child) {
            if (questionService.isLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (questionService.hasCompletedAllToday) {
              return _buildCompletedView(questionService);
            }

            final currentQuestion = questionService.currentQuestion;
            if (currentQuestion == null) {
              return _buildNoQuestionsView();
            }

            return _buildQuestionView(currentQuestion, questionService);
          },
        ),
      ),
    );
  }

  /// بناء عرض السؤال
  Widget _buildQuestionView(DailyQuestion question, DailyQuestionService service) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // مؤشر التقدم
            _buildProgressIndicator(service),
            const SizedBox(height: 20),
            
            // بطاقة السؤال
            _buildQuestionCard(question),
            const SizedBox(height: 20),
            
            // خيارات الإجابة
            _buildAnswerOptions(question),
            const SizedBox(height: 20),
            
            // زر الإجابة
            if (_selectedAnswerIndex != null && !_showResult)
              _buildSubmitButton(question, service),
            
            // عرض النتيجة
            if (_showResult)
              _buildResultView(question),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر التقدم
  Widget _buildProgressIndicator(DailyQuestionService service) {
    final progress = (service.currentQuestionIndex + 1) / service.totalDailyQuestions;
    
    return Column(
      children: [
        Text(
          'السؤال ${service.currentQuestionIndex + 1} من ${service.totalDailyQuestions}',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة السؤال
  Widget _buildQuestionCard(DailyQuestion question) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التصنيف: ${question.category}',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              question.question,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء خيارات الإجابة
  Widget _buildAnswerOptions(DailyQuestion question) {
    return Column(
      children: question.options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedAnswerIndex == index;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: InkWell(
            onTap: _showResult ? null : () {
              setState(() {
                _selectedAnswerIndex = index;
              });
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: isSelected 
                    ? Theme.of(context).primaryColor.withOpacity(0.1)
                    : Colors.transparent,
                border: Border.all(
                  color: isSelected 
                      ? Theme.of(context).primaryColor
                      : Colors.grey[300]!,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected 
                      ? Theme.of(context).primaryColor
                      : null,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton(DailyQuestion question, DailyQuestionService service) {
    return ElevatedButton(
      onPressed: () async {
        if (_selectedAnswerIndex != null) {
          final isCorrect = await service.answerCurrentQuestion(_selectedAnswerIndex!);
          setState(() {
            _isAnswerCorrect = isCorrect;
            _showResult = true;
          });
        }
      },
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: const Text(
        'إرسال الإجابة',
        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
    );
  }

  /// بناء عرض النتيجة
  Widget _buildResultView(DailyQuestion question) {
    return Card(
      color: _isAnswerCorrect ? Colors.green[50] : Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              _isAnswerCorrect ? Icons.check_circle : Icons.cancel,
              color: _isAnswerCorrect ? Colors.green : Colors.red,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              _isAnswerCorrect ? 'إجابة صحيحة!' : 'إجابة خاطئة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _isAnswerCorrect ? Colors.green : Colors.red,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'الإجابة الصحيحة: ${question.options[question.correctAnswerIndex]}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              question.explanation,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _selectedAnswerIndex = null;
                  _showResult = false;
                });
                _animationController.reset();
                _animationController.forward();
              },
              child: const Text('السؤال التالي'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عرض الإكمال
  Widget _buildCompletedView(DailyQuestionService service) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.celebration,
                  size: 64,
                  color: Colors.green,
                ),
                const SizedBox(height: 16),
                const Text(
                  'تهانينا!',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'لقد أكملت جميع الأسئلة اليوم',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'النتيجة: ${service.correctAnswersCount}/${service.totalDailyQuestions}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'النسبة: ${service.correctAnswersPercentage.toStringAsFixed(1)}%',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('العودة للرئيسية'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء عرض عدم وجود أسئلة
  Widget _buildNoQuestionsView() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.help_outline,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد أسئلة متاحة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'يرجى المحاولة مرة أخرى لاحقاً',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
