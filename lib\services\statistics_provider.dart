import 'package:flutter/material.dart';
import '../models/statistics_model.dart';
import '../models/achievement_model.dart';
import '../services/statistics_service.dart';
import '../utils/logger.dart';

/// مزود الإحصائيات والإنجازات
class StatisticsProvider extends ChangeNotifier {
  final StatisticsService _statisticsService = StatisticsService();

  // البيانات الحالية
  DailyStatistics? _todayStats;
  WeeklyStatistics? _weeklyStats;
  MonthlyStatistics? _monthlyStats;
  StreakData _streakData = StreakData(currentStreak: 0, longestStreak: 0);
  List<Achievement> _achievements = [];
  List<DailyStatistics> _heatMapData = [];

  // حالة التحميل
  bool _isLoading = true;
  bool _isInitialized = false;

  // Getters
  DailyStatistics? get todayStats => _todayStats;
  WeeklyStatistics? get weeklyStats => _weeklyStats;
  MonthlyStatistics? get monthlyStats => _monthlyStats;
  StreakData get streakData => _streakData;
  List<Achievement> get achievements => _achievements;
  List<DailyStatistics> get heatMapData => _heatMapData;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;

  /// تهيئة المزود
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _isLoading = true;
      notifyListeners();

      await _loadAllData();

      _isInitialized = true;
      AppLogger.info('تم تهيئة مزود الإحصائيات بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود الإحصائيات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل جميع البيانات
  Future<void> _loadAllData() async {
    await Future.wait([
      _loadTodayStats(),
      _loadWeeklyStats(),
      _loadMonthlyStats(),
      _loadStreakData(),
      _loadAchievements(),
      _loadHeatMapData(),
    ]);
  }

  /// تحميل إحصائيات اليوم
  Future<void> _loadTodayStats() async {
    try {
      final today = DateTime.now();
      _todayStats = await _statisticsService.getDailyStatistics(today);

      // إنشاء إحصائيات افتراضية إذا لم توجد
      _todayStats ??= DailyStatistics(
        date: today,
        completedAzkar: 0,
        totalAzkar: 0,
        completedCategories: 0,
        totalCategories: 0,
        points: 0,
        timeSpent: Duration.zero,
      );
    } catch (e) {
      AppLogger.error('خطأ في تحميل إحصائيات اليوم: $e');
    }
  }

  /// تحميل الإحصائيات الأسبوعية
  Future<void> _loadWeeklyStats() async {
    try {
      final now = DateTime.now();
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      _weeklyStats = await _statisticsService.getWeeklyStatistics(weekStart);
    } catch (e) {
      AppLogger.error('خطأ في تحميل الإحصائيات الأسبوعية: $e');
    }
  }

  /// تحميل الإحصائيات الشهرية
  Future<void> _loadMonthlyStats() async {
    try {
      final now = DateTime.now();
      _monthlyStats = await _statisticsService.getMonthlyStatistics(now);
    } catch (e) {
      AppLogger.error('خطأ في تحميل الإحصائيات الشهرية: $e');
    }
  }

  /// تحميل بيانات السلسلة المتتالية
  Future<void> _loadStreakData() async {
    try {
      _streakData = await _statisticsService.getStreakData();
    } catch (e) {
      AppLogger.error('خطأ في تحميل بيانات السلسلة: $e');
    }
  }

  /// تحميل الإنجازات
  Future<void> _loadAchievements() async {
    try {
      _achievements = await _statisticsService.getAllAchievements();
    } catch (e) {
      AppLogger.error('خطأ في تحميل الإنجازات: $e');
    }
  }

  /// تحميل بيانات الخريطة الحرارية (آخر 90 يوماً)
  Future<void> _loadHeatMapData() async {
    try {
      _heatMapData = [];
      final now = DateTime.now();

      for (int i = 89; i >= 0; i--) {
        final date = now.subtract(Duration(days: i));
        final stats = await _statisticsService.getDailyStatistics(date);

        if (stats != null) {
          _heatMapData.add(stats);
        } else {
          // إضافة يوم فارغ
          _heatMapData.add(DailyStatistics(
            date: date,
            completedAzkar: 0,
            totalAzkar: 0,
            completedCategories: 0,
            totalCategories: 0,
            points: 0,
            timeSpent: Duration.zero,
          ));
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل بيانات الخريطة الحرارية: $e');
    }
  }

  /// تسجيل إكمال ذكر
  Future<void> recordZikrCompletion({
    required int completedCount,
    required int totalCount,
    required String category,
    required Duration timeSpent,
  }) async {
    try {
      final today = DateTime.now();
      final currentStats = _todayStats ??
          DailyStatistics(
            date: today,
            completedAzkar: 0,
            totalAzkar: 0,
            completedCategories: 0,
            totalCategories: 0,
            points: 0,
            timeSpent: Duration.zero,
          );

      // حساب النقاط (10 نقاط لكل ذكر مكتمل)
      final newPoints = completedCount * 10;

      // تحديث الإحصائيات
      final updatedStats = currentStats.copyWith(
        completedAzkar: currentStats.completedAzkar + completedCount,
        totalAzkar: currentStats.totalAzkar + totalCount,
        points: currentStats.points + newPoints,
        timeSpent: currentStats.timeSpent + timeSpent,
      );

      // حفظ في قاعدة البيانات
      await _statisticsService.saveDailyStatistics(updatedStats);
      _todayStats = updatedStats;

      // تحديث السلسلة المتتالية
      await _updateStreak();

      // فحص الإنجازات
      await _checkAchievements();

      notifyListeners();
      AppLogger.info('تم تسجيل إكمال الذكر بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تسجيل إكمال الذكر: $e');
    }
  }

  /// تحديث السلسلة المتتالية
  Future<void> _updateStreak() async {
    try {
      final today = DateTime.now();
      final yesterday = today.subtract(const Duration(days: 1));

      // فحص إذا كان اليوم مكتملاً
      final todayComplete = _todayStats?.isFullyCompleted ?? false;

      if (todayComplete) {
        // فحص إذا كان أمس مكتملاً أيضاً
        final yesterdayStats =
            await _statisticsService.getDailyStatistics(yesterday);
        final yesterdayComplete = yesterdayStats?.isFullyCompleted ?? false;

        int newCurrentStreak;
        if (yesterdayComplete || _streakData.currentStreak == 0) {
          // استمرار السلسلة أو بداية جديدة
          newCurrentStreak = _streakData.currentStreak + 1;
        } else {
          // بداية سلسلة جديدة
          newCurrentStreak = 1;
        }

        final newLongestStreak = newCurrentStreak > _streakData.longestStreak
            ? newCurrentStreak
            : _streakData.longestStreak;

        _streakData = _streakData.copyWith(
          currentStreak: newCurrentStreak,
          longestStreak: newLongestStreak,
          lastActiveDate: today,
          streakStartDate: _streakData.streakStartDate ?? today,
        );

        await _statisticsService.updateStreakData(_streakData);
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث السلسلة المتتالية: $e');
    }
  }

  /// فحص وتحديث الإنجازات
  Future<void> _checkAchievements() async {
    try {
      bool hasNewAchievement = false;

      for (int i = 0; i < _achievements.length; i++) {
        final achievement = _achievements[i];
        if (achievement.isUnlocked) continue;

        int newCurrentValue = achievement.currentValue;

        // فحص نوع الإنجاز وتحديث القيمة الحالية
        switch (achievement.type) {
          case AchievementType.streak:
            newCurrentValue = _streakData.currentStreak;
            break;
          case AchievementType.daily:
            newCurrentValue = _todayStats?.isFullyCompleted == true ? 1 : 0;
            break;
          case AchievementType.total:
            // حساب إجمالي الأذكار المكتملة
            newCurrentValue = await _getTotalCompletedAzkar();
            break;
          case AchievementType.special:
            // إنجازات خاصة (مثل أذكار الصباح/المساء)
            newCurrentValue = await _getSpecialAchievementValue(achievement.id);
            break;
          default:
            continue;
        }

        // تحديث الإنجاز إذا تغيرت القيمة
        if (newCurrentValue != achievement.currentValue) {
          final updatedAchievement = achievement.copyWith(
            currentValue: newCurrentValue,
            isUnlocked: newCurrentValue >= achievement.targetValue,
            unlockedDate: newCurrentValue >= achievement.targetValue
                ? DateTime.now()
                : null,
          );

          _achievements[i] = updatedAchievement;
          await _statisticsService.updateAchievement(updatedAchievement);

          if (updatedAchievement.isUnlocked && !achievement.isUnlocked) {
            hasNewAchievement = true;
            AppLogger.info('تم فتح إنجاز جديد: ${updatedAchievement.title}');
          }
        }
      }

      if (hasNewAchievement) {
        // يمكن إضافة إشعار أو تأثير بصري هنا
      }
    } catch (e) {
      AppLogger.error('خطأ في فحص الإنجازات: $e');
    }
  }

  /// الحصول على إجمالي الأذكار المكتملة
  Future<int> _getTotalCompletedAzkar() async {
    // هذه دالة مبسطة، يمكن تحسينها لاحقاً
    return _todayStats?.completedAzkar ?? 0;
  }

  /// الحصول على قيمة الإنجاز الخاص
  Future<int> _getSpecialAchievementValue(String achievementId) async {
    // هذه دالة مبسطة، يمكن تحسينها لاحقاً
    return 0;
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    await _loadAllData();
    notifyListeners();
  }

  /// الحصول على الإنجازات المفتوحة
  List<Achievement> get unlockedAchievements =>
      _achievements.where((a) => a.isUnlocked).toList();

  /// الحصول على الإنجازات المقفلة
  List<Achievement> get lockedAchievements =>
      _achievements.where((a) => !a.isUnlocked).toList();

  /// الحصول على إجمالي النقاط
  int get totalPoints => _achievements
      .where((a) => a.isUnlocked)
      .fold(0, (sum, a) => sum + a.points);

  /// تسجيل عدد الأذكار
  Future<void> recordZikrCount(String zikr, int count) async {
    try {
      // تسجيل إكمال الذكر مع معلومات أساسية
      await recordZikrCompletion(
        completedCount: count,
        totalCount: count,
        category: 'عام',
        timeSpent: const Duration(seconds: 30), // وقت افتراضي
      );
      AppLogger.info('تم تسجيل $count من الذكر: $zikr');
    } catch (e) {
      AppLogger.error('خطأ في تسجيل عدد الأذكار: $e');
    }
  }

  /// الحصول على إحصائيات الأذكار
  Map<String, int> getZikrStatistics() {
    try {
      // إرجاع إحصائيات بسيطة من البيانات الحالية
      return {
        'اليوم': _todayStats?.completedAzkar ?? 0,
        'هذا الأسبوع': _weeklyStats?.totalCompletedAzkar ?? 0,
        'هذا الشهر': _monthlyStats?.totalCompletedAzkar ?? 0,
        'السلسلة الحالية': _streakData.currentStreak,
        'أطول سلسلة': _streakData.longestStreak,
      };
    } catch (e) {
      AppLogger.error('خطأ في الحصول على إحصائيات الأذكار: $e');
      return {};
    }
  }
}
