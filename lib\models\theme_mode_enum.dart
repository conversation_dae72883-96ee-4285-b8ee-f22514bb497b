/// تعداد أوضاع السمة المختلفة في التطبيق
enum AppThemeMode {
  /// الوضع الفاتح
  light,
  
  /// الوضع الخافت
  dim,
  
  /// الوضع المظلم
  dark,
}

/// امتداد لإضافة وظائف مفيدة لتعداد أوضاع السمة
extension AppThemeModeExtension on AppThemeMode {
  /// الحصول على اسم الوضع باللغة العربية
  String get arabicName {
    switch (this) {
      case AppThemeMode.light:
        return 'فاتح';
      case AppThemeMode.dim:
        return 'خافت';
      case AppThemeMode.dark:
        return 'مظلم';
    }
  }

  /// الحصول على اسم الوضع باللغة الإنجليزية
  String get englishName {
    switch (this) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dim:
        return 'Dim';
      case AppThemeMode.dark:
        return 'Dark';
    }
  }

  /// تحويل النص إلى وضع السمة
  static AppThemeMode fromString(String value) {
    switch (value.toLowerCase()) {
      case 'light':
        return AppThemeMode.light;
      case 'dim':
        return AppThemeMode.dim;
      case 'dark':
        return AppThemeMode.dark;
      default:
        return AppThemeMode.light; // الافتراضي
    }
  }

  /// تحويل وضع السمة إلى نص
  String toStringValue() {
    switch (this) {
      case AppThemeMode.light:
        return 'light';
      case AppThemeMode.dim:
        return 'dim';
      case AppThemeMode.dark:
        return 'dark';
    }
  }
}
