import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_view_mode.dart';
import '../models/theme_mode_enum.dart';

class ThemeProvider extends ChangeNotifier {
  // طريقة عرض القرآن الكريم
  QuranViewMode _quranViewMode = QuranViewMode.list; // القيمة الافتراضية

  // وضع السمة الحالي
  AppThemeMode _themeMode = AppThemeMode.light;

  // السمة الفاتحة
  ThemeData? _lightTheme;

  // السمة المظلمة
  ThemeData? _darkTheme;

  // الحصول على طريقة عرض القرآن الكريم
  QuranViewMode get quranViewMode => _quranViewMode;

  // الحصول على وضع السمة الحالي
  AppThemeMode get themeMode => _themeMode;

  // الحصول على السمة الفاتحة
  ThemeData get lightTheme => _lightTheme ?? _getDefaultLightTheme();

  // الحصول على السمة الحالية
  ThemeData get currentTheme => _darkTheme ?? _getDefaultDarkTheme();

  // المُنشئ - يقوم بتحميل الإعدادات عند إنشاء المزود
  ThemeProvider() {
    _loadThemePreference();
  }

  // تحميل إعدادات السمة من التخزين المحلي
  Future<void> _loadThemePreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // تحميل طريقة عرض القرآن الكريم
    String quranViewModeStr = prefs.getString('quran_view_mode') ?? 'list';
    _quranViewMode = _getQuranViewModeFromString(quranViewModeStr);

    // تحميل وضع السمة
    String themeModeStr = prefs.getString('theme_mode') ?? 'light';
    _themeMode = AppThemeModeExtension.fromString(themeModeStr);

    notifyListeners();
  }

  /// الحصول على السمة الافتراضية الفاتحة
  ThemeData _getDefaultLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primarySwatch: Colors.green,
      primaryColor: Colors.green,
      fontFamily: 'Amiri',
    );
  }

  /// الحصول على السمة الافتراضية المظلمة
  ThemeData _getDefaultDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: Colors.green,
      primaryColor: Colors.green,
      fontFamily: 'Amiri',
    );
  }

  /// تغيير وضع السمة
  Future<void> setThemeMode(AppThemeMode mode) async {
    _themeMode = mode;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme_mode', mode.toStringValue());
    notifyListeners();
  }

  // تحويل النص إلى طريقة عرض القرآن الكريم
  QuranViewMode _getQuranViewModeFromString(String value) {
    switch (value) {
      case 'list':
        return QuranViewMode.list;
      case 'pages':
        return QuranViewMode.pages;
      case 'mushaf':
        return QuranViewMode.mushaf;
      default:
        return QuranViewMode.list;
    }
  }

  // تغيير طريقة عرض القرآن الكريم
  Future<void> setQuranViewMode(QuranViewMode mode) async {
    if (_quranViewMode != mode) {
      _quranViewMode = mode;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'quran_view_mode',
        _quranViewMode.toString().split('.').last,
      );

      // إخطار المستمعين بالتغيير
      notifyListeners();

      // طباعة معلومات طريقة العرض للتأكد من تغييرها
      debugPrint(
        'Quran view mode changed to: ${_quranViewMode.toString().split('.').last}',
      );
    }
  }
}
