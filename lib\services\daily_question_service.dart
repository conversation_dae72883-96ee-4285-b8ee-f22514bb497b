import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import 'dart:math';

/// نموذج السؤال اليومي
class DailyQuestion {
  final String id;
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final String explanation;
  final String category;
  final DateTime date;

  DailyQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    required this.explanation,
    required this.category,
    required this.date,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'explanation': explanation,
      'category': category,
      'date': date.toIso8601String(),
    };
  }

  factory DailyQuestion.fromJson(Map<String, dynamic> json) {
    return DailyQuestion(
      id: json['id'],
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswerIndex: json['correctAnswerIndex'],
      explanation: json['explanation'],
      category: json['category'],
      date: DateTime.parse(json['date']),
    );
  }
}

/// نموذج إجابة المستخدم
class UserAnswer {
  final String questionId;
  final int selectedAnswerIndex;
  final bool isCorrect;
  final DateTime answeredAt;

  UserAnswer({
    required this.questionId,
    required this.selectedAnswerIndex,
    required this.isCorrect,
    required this.answeredAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'selectedAnswerIndex': selectedAnswerIndex,
      'isCorrect': isCorrect,
      'answeredAt': answeredAt.toIso8601String(),
    };
  }

  factory UserAnswer.fromJson(Map<String, dynamic> json) {
    return UserAnswer(
      questionId: json['questionId'],
      selectedAnswerIndex: json['selectedAnswerIndex'],
      isCorrect: json['isCorrect'],
      answeredAt: DateTime.parse(json['answeredAt']),
    );
  }
}

/// خدمة الأسئلة اليومية
class DailyQuestionService extends ChangeNotifier {
  List<DailyQuestion> _todayQuestions = [];
  List<UserAnswer> _userAnswers = [];
  bool _isLoading = false;
  int _currentQuestionIndex = 0;

  /// قائمة الأسئلة الافتراضية
  static final List<DailyQuestion> _defaultQuestions = [
    DailyQuestion(
      id: '1',
      question: 'كم عدد السور في القرآن الكريم؟',
      options: ['114', '113', '115', '112'],
      correctAnswerIndex: 0,
      explanation: 'القرآن الكريم يحتوي على 114 سورة',
      category: 'القرآن الكريم',
      date: DateTime.now(),
    ),
    DailyQuestion(
      id: '2',
      question: 'ما هي أطول سورة في القرآن الكريم؟',
      options: ['سورة البقرة', 'سورة آل عمران', 'سورة النساء', 'سورة المائدة'],
      correctAnswerIndex: 0,
      explanation: 'سورة البقرة هي أطول سورة في القرآن الكريم',
      category: 'القرآن الكريم',
      date: DateTime.now(),
    ),
    DailyQuestion(
      id: '3',
      question: 'كم عدد أركان الإسلام؟',
      options: ['4', '5', '6', '7'],
      correctAnswerIndex: 1,
      explanation: 'أركان الإسلام خمسة: الشهادتان، الصلاة، الزكاة، الصوم، الحج',
      category: 'أركان الإسلام',
      date: DateTime.now(),
    ),
  ];

  /// الحصول على أسئلة اليوم
  List<DailyQuestion> get todayQuestions => _todayQuestions;

  /// الحصول على إجابات المستخدم
  List<UserAnswer> get userAnswers => _userAnswers;

  /// الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على فهرس السؤال الحالي
  int get currentQuestionIndex => _currentQuestionIndex;

  /// الحصول على السؤال الحالي
  DailyQuestion? get currentQuestion {
    if (_currentQuestionIndex < _todayQuestions.length) {
      return _todayQuestions[_currentQuestionIndex];
    }
    return null;
  }

  /// التحقق من انتهاء جميع الأسئلة
  bool get isAllQuestionsAnswered => _userAnswers.length >= _todayQuestions.length;

  /// الحصول على عدد الإجابات الصحيحة
  int get correctAnswersCount => _userAnswers.where((answer) => answer.isCorrect).length;

  /// الحصول على النسبة المئوية للإجابات الصحيحة
  double get correctAnswersPercentage {
    if (_userAnswers.isEmpty) return 0.0;
    return (correctAnswersCount / _userAnswers.length) * 100;
  }

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      await _loadTodayQuestions();
      await _loadUserAnswers();
      AppLogger.info('تم تهيئة خدمة الأسئلة اليومية بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة الأسئلة اليومية: $e');
    }
  }

  /// تحميل أسئلة اليوم
  Future<void> _loadTodayQuestions() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      
      // التحقق من وجود أسئلة محفوظة لليوم الحالي
      final savedQuestionsJson = prefs.getStringList('daily_questions_$todayString');
      
      if (savedQuestionsJson != null && savedQuestionsJson.isNotEmpty) {
        // تحميل الأسئلة المحفوظة
        _todayQuestions = savedQuestionsJson.map((json) {
          final Map<String, dynamic> data = Map<String, dynamic>.from(
            Uri.splitQueryString(json),
          );
          return DailyQuestion.fromJson(data);
        }).toList();
      } else {
        // إنشاء أسئلة جديدة لليوم
        await _generateTodayQuestions();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      AppLogger.error('خطأ في تحميل أسئلة اليوم: $e');
    }
  }

  /// إنشاء أسئلة اليوم
  Future<void> _generateTodayQuestions() async {
    try {
      final random = Random();
      final selectedQuestions = <DailyQuestion>[];
      
      // اختيار 3 أسئلة عشوائية
      final shuffledQuestions = List<DailyQuestion>.from(_defaultQuestions);
      shuffledQuestions.shuffle(random);
      
      selectedQuestions.addAll(shuffledQuestions.take(3));
      
      // تحديث تاريخ الأسئلة
      _todayQuestions = selectedQuestions.map((question) {
        return DailyQuestion(
          id: question.id,
          question: question.question,
          options: question.options,
          correctAnswerIndex: question.correctAnswerIndex,
          explanation: question.explanation,
          category: question.category,
          date: DateTime.now(),
        );
      }).toList();
      
      await _saveTodayQuestions();
      AppLogger.info('تم إنشاء ${_todayQuestions.length} أسئلة لليوم');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء أسئلة اليوم: $e');
    }
  }

  /// حفظ أسئلة اليوم
  Future<void> _saveTodayQuestions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      
      final questionsJson = _todayQuestions.map((question) {
        return Uri(queryParameters: question.toJson().map(
          (key, value) => MapEntry(key, value.toString()),
        )).query;
      }).toList();
      
      await prefs.setStringList('daily_questions_$todayString', questionsJson);
    } catch (e) {
      AppLogger.error('خطأ في حفظ أسئلة اليوم: $e');
    }
  }

  /// تحميل إجابات المستخدم
  Future<void> _loadUserAnswers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      
      final answersJson = prefs.getStringList('user_answers_$todayString') ?? [];
      
      _userAnswers = answersJson.map((json) {
        final Map<String, dynamic> data = Map<String, dynamic>.from(
          Uri.splitQueryString(json),
        );
        return UserAnswer.fromJson(data);
      }).toList();
      
      // تحديث فهرس السؤال الحالي
      _currentQuestionIndex = _userAnswers.length;
    } catch (e) {
      AppLogger.error('خطأ في تحميل إجابات المستخدم: $e');
    }
  }

  /// حفظ إجابة المستخدم
  Future<void> _saveUserAnswers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      
      final answersJson = _userAnswers.map((answer) {
        return Uri(queryParameters: answer.toJson().map(
          (key, value) => MapEntry(key, value.toString()),
        )).query;
      }).toList();
      
      await prefs.setStringList('user_answers_$todayString', answersJson);
    } catch (e) {
      AppLogger.error('خطأ في حفظ إجابات المستخدم: $e');
    }
  }

  /// الإجابة على السؤال الحالي
  Future<bool> answerCurrentQuestion(int selectedAnswerIndex) async {
    try {
      if (currentQuestion == null) return false;
      
      final isCorrect = selectedAnswerIndex == currentQuestion!.correctAnswerIndex;
      
      final userAnswer = UserAnswer(
        questionId: currentQuestion!.id,
        selectedAnswerIndex: selectedAnswerIndex,
        isCorrect: isCorrect,
        answeredAt: DateTime.now(),
      );
      
      _userAnswers.add(userAnswer);
      _currentQuestionIndex++;
      
      await _saveUserAnswers();
      notifyListeners();
      
      AppLogger.info('تم الإجابة على السؤال: ${currentQuestion!.question} - ${isCorrect ? 'صحيح' : 'خطأ'}');
      
      return isCorrect;
    } catch (e) {
      AppLogger.error('خطأ في الإجابة على السؤال: $e');
      return false;
    }
  }

  /// إعادة تعيين الأسئلة والإجابات
  Future<void> resetDailyQuestions() async {
    try {
      _todayQuestions.clear();
      _userAnswers.clear();
      _currentQuestionIndex = 0;
      
      await _generateTodayQuestions();
      notifyListeners();
      
      AppLogger.info('تم إعادة تعيين الأسئلة اليومية');
    } catch (e) {
      AppLogger.error('خطأ في إعادة تعيين الأسئلة: $e');
    }
  }
}
